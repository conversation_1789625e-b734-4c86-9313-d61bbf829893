{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseNextInvalidImportError.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { formatModuleTrace, getModuleTrace } from './getModuleTrace'\nimport { SimpleWebpackError } from './simpleWebpackError'\n\nexport function getNextInvalidImportError(\n  err: Error,\n  module: any,\n  compilation: webpack.Compilation,\n  compiler: webpack.Compiler\n): SimpleWebpackError | false {\n  try {\n    if (\n      !module.loaders.find((loader: any) =>\n        loader.loader.includes('next-invalid-import-error-loader.js')\n      )\n    ) {\n      return false\n    }\n\n    const { moduleTrace } = getModuleTrace(module, compilation, compiler)\n    const { formattedModuleTrace, lastInternalFileName, invalidImportMessage } =\n      formatModuleTrace(compiler, moduleTrace)\n\n    return new SimpleWebpackError(\n      lastInternalFileName,\n      err.message +\n        invalidImportMessage +\n        '\\n\\nImport trace for requested module:\\n' +\n        formattedModuleTrace\n    )\n  } catch {\n    return false\n  }\n}\n"], "names": ["getNextInvalidImportError", "err", "module", "compilation", "compiler", "loaders", "find", "loader", "includes", "moduleTrace", "getModuleTrace", "formattedModuleTrace", "lastInternalFileName", "invalidImportMessage", "formatModuleTrace", "SimpleWebpackError", "message"], "mappings": ";;;;+BAIgBA;;;eAAAA;;;gCAHkC;oCACf;AAE5B,SAASA,0BACdC,GAAU,EACVC,MAAW,EACXC,WAAgC,EAChCC,QAA0B;IAE1B,IAAI;QACF,IACE,CAACF,OAAOG,OAAO,CAACC,IAAI,CAAC,CAACC,SACpBA,OAAOA,MAAM,CAACC,QAAQ,CAAC,yCAEzB;YACA,OAAO;QACT;QAEA,MAAM,EAAEC,WAAW,EAAE,GAAGC,IAAAA,8BAAc,EAACR,QAAQC,aAAaC;QAC5D,MAAM,EAAEO,oBAAoB,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAE,GACxEC,IAAAA,iCAAiB,EAACV,UAAUK;QAE9B,OAAO,IAAIM,sCAAkB,CAC3BH,sBACAX,IAAIe,OAAO,GACTH,uBACA,6CACAF;IAEN,EAAE,OAAM;QACN,OAAO;IACT;AACF"}