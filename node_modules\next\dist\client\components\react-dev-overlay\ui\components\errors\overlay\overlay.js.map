{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/overlay/overlay.tsx"], "sourcesContent": ["import { Overlay, type OverlayProps } from '../../overlay/overlay'\n\nexport function ErrorOverlayOverlay({ children, ...props }: OverlayProps) {\n  return <Overlay {...props}>{children}</Overlay>\n}\n\nexport const OVERLAY_STYLES = `\n  [data-nextjs-dialog-overlay] {\n    padding: initial;\n    top: 10vh;\n  }\n`\n"], "names": ["ErrorOverlayOverlay", "OVERLAY_STYLES", "children", "props", "Overlay"], "mappings": ";;;;;;;;;;;;;;;IAEgBA,mBAAmB;eAAnBA;;IAIHC,cAAc;eAAdA;;;;yBAN8B;AAEpC,SAASD,oBAAoB,KAAoC;IAApC,IAAA,EAAEE,QAAQ,EAAE,GAAGC,OAAqB,GAApC;IAClC,qBAAO,qBAACC,gBAAO;QAAE,GAAGD,KAAK;kBAAGD;;AAC9B;AAEO,MAAMD,iBAAkB"}