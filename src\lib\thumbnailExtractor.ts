// Thumbnail extraction utility for product URLs

export interface ProductThumbnail {
  url: string;
  alt: string;
  fallback: string;
}

// Extract domain from URL for fallback logic
function getDomain(url: string): string {
  try {
    return new URL(url).hostname.replace('www.', '');
  } catch {
    return 'unknown';
  }
}

// Generate fallback thumbnail based on product title and domain
function generateFallbackThumbnail(title: string): ProductThumbnail {
  const colors = [
    'from-blue-500 to-purple-600',
    'from-green-500 to-blue-600', 
    'from-red-500 to-pink-600',
    'from-yellow-500 to-orange-600',
    'from-purple-500 to-pink-600',
    'from-cyan-500 to-blue-600',
    'from-orange-500 to-red-600',
    'from-teal-500 to-green-600'
  ];
  
  // Use title hash to consistently pick a color
  const colorIndex = title.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colors.length;
  
  return {
    url: '', // No actual image URL
    alt: title,
    fallback: colors[colorIndex]
  };
}

// Extract thumbnail from different e-commerce platforms
export async function extractThumbnail(productUrl: string, title: string): Promise<ProductThumbnail> {
  const domain = getDomain(productUrl);
  
  try {
    // For Amazon products, try to extract ASIN and use Amazon's image API
    if (domain.includes('amazon')) {
      const asinMatch = productUrl.match(/\/dp\/([A-Z0-9]{10})/);
      if (asinMatch) {
        const asin = asinMatch[1];
        return {
          url: `https://images-na.ssl-images-amazon.com/images/P/${asin}.01.L.jpg`,
          alt: title,
          fallback: generateFallbackThumbnail(title).fallback
        };
      }
    }
    
    // For eBay products
    if (domain.includes('ebay')) {
      // eBay thumbnails are harder to extract without API, use fallback
      return generateFallbackThumbnail(title);
    }
    
    // For Steam products
    if (domain.includes('steampowered') || domain.includes('store.steampowered')) {
      const appIdMatch = productUrl.match(/\/app\/(\d+)/);
      if (appIdMatch) {
        const appId = appIdMatch[1];
        return {
          url: `https://cdn.akamai.steamstatic.com/steam/apps/${appId}/header.jpg`,
          alt: title,
          fallback: generateFallbackThumbnail(title).fallback
        };
      }
    }
    
    // For Best Buy products
    if (domain.includes('bestbuy')) {
      // Best Buy uses product SKU in URL
      const skuMatch = productUrl.match(/\/(\d{7})/);
      if (skuMatch) {
        // Best Buy doesn't have a direct image API, use fallback
        return generateFallbackThumbnail(title);
      }
    }
    
    // For Newegg products
    if (domain.includes('newegg')) {
      return generateFallbackThumbnail(title);
    }

    // Default fallback for unknown domains
    return generateFallbackThumbnail(title);
    
  } catch (error) {
    console.error('Error extracting thumbnail:', error);
    return generateFallbackThumbnail(title);
  }
}

// Get product emoji based on title keywords
export function getProductEmoji(title: string): string {
  const titleLower = title.toLowerCase();
  
  if (titleLower.includes('controller') || titleLower.includes('gamepad')) return '🎮';
  if (titleLower.includes('headset') || titleLower.includes('headphone')) return '🎧';
  if (titleLower.includes('keyboard')) return '⌨️';
  if (titleLower.includes('mouse')) return '🖱️';
  if (titleLower.includes('monitor') || titleLower.includes('display')) return '🖥️';
  if (titleLower.includes('chair')) return '🪑';
  if (titleLower.includes('microphone') || titleLower.includes('mic')) return '🎤';
  if (titleLower.includes('webcam') || titleLower.includes('camera')) return '📷';
  if (titleLower.includes('laptop') || titleLower.includes('computer')) return '💻';
  if (titleLower.includes('tablet')) return '📱';
  if (titleLower.includes('speaker')) return '🔊';
  if (titleLower.includes('cable') || titleLower.includes('cord')) return '🔌';
  if (titleLower.includes('stand') || titleLower.includes('mount')) return '🗂️';
  if (titleLower.includes('light') || titleLower.includes('led') || titleLower.includes('rgb')) return '💡';
  if (titleLower.includes('case') || titleLower.includes('cover')) return '📦';
  
  return '🎮'; // Default gaming emoji
}

// Calculate discount percentage
export function calculateDiscount(originalPrice: number, salePrice: number): number {
  return Math.round(((originalPrice - salePrice) / originalPrice) * 100);
}
