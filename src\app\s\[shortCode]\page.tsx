'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Script from 'next/script';

interface LinkData {
  originalUrl: string;
  clicks: number;
  createdAt: string;
}

export default function RedirectPage() {
  const params = useParams();
  const shortCode = params.shortCode as string;
  
  const [linkData, setLinkData] = useState<LinkData | null>(null);
  const [countdown, setCountdown] = useState(10);
  const [showAd, setShowAd] = useState(true);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Fetch the original URL
    const fetchLinkData = async () => {
      try {
        const response = await fetch(`/api/redirect/${shortCode}`);
        if (!response.ok) {
          throw new Error('Link not found');
        }
        const data = await response.json();
        setLinkData(data);
        setLoading(false);
      } catch {
        setError('Link not found or expired');
        setLoading(false);
      }
    };

    fetchLinkData();
  }, [shortCode]);

  useEffect(() => {
    if (!showAd || countdown <= 0) return;

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          setShowAd(false);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [showAd, countdown]);

  const handleAdClick = () => {
    // Simulate ad click - in real implementation, this would track the click
    console.log('Ad clicked!');
    // You can add actual ad tracking here
  };

  const handleSkipAd = () => {
    if (countdown <= 0) {
      setShowAd(false);
    }
  };

  const handleRedirect = () => {
    if (linkData) {
      // Track the click
      fetch(`/api/redirect/${shortCode}`, { method: 'POST' });
      // Redirect to original URL
      window.location.href = linkData.originalUrl;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400 mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">404</h1>
          <p className="text-gray-300 mb-8">{error}</p>
          <Link href="/" className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all">
            Go Home
          </Link>
        </div>
      </div>
    );
  }

  if (showAd) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* Header */}
        <nav className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4">
          <div className="max-w-4xl mx-auto flex justify-between items-center">
            <Link href="/" className="text-2xl font-bold text-white">
              🎮 <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">GameHub</span>
            </Link>
            <div className="text-purple-400 font-mono">
              Redirecting in {countdown}s
            </div>
          </div>
        </nav>

        <div className="flex items-center justify-center min-h-[calc(100vh-80px)] p-4">
          <div className="max-w-2xl w-full">
            {/* Ad Section */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-6">
              <h2 className="text-2xl font-bold text-white mb-4 text-center">
                🎮 Support GameHub - Click the Ad Below!
              </h2>
              
              {/* Montag Ad Network */}
              <div className="bg-white/5 rounded-lg p-6 mb-6 text-center">
                <p className="text-gray-300 text-sm mb-4">Advertisement</p>
                <div id="montag-ad-zone" className="min-h-[200px] flex items-center justify-center">
                  {/* Montag ads will load here */}
                  <div className="text-gray-400 text-sm">Loading ads...</div>
                </div>
              </div>

              {/* Fallback Gaming Ad */}
              <div
                onClick={handleAdClick}
                className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 cursor-pointer hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 mb-6"
              >
                <div className="text-center text-white">
                  <h3 className="text-lg font-bold mb-2">🚀 Epic Gaming Deals!</h3>
                  <p className="mb-3">Get up to 70% off on the latest games and gaming gear!</p>
                  <div className="bg-white/20 rounded-lg p-3">
                    <p className="text-sm font-semibold">Limited Time Offer</p>
                    <p className="text-xs opacity-90">Click to explore amazing deals</p>
                  </div>
                </div>
              </div>

              {/* Countdown */}
              <div className="text-center">
                <div className="bg-purple-600 text-white rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                  {countdown}
                </div>
                <p className="text-gray-300 mb-4">
                  Please wait {countdown} seconds before accessing your link
                </p>
                
                {countdown <= 0 ? (
                  <button
                    onClick={handleSkipAd}
                    className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-105"
                  >
                    Continue to Link →
                  </button>
                ) : (
                  <div className="bg-gray-600 text-gray-300 px-8 py-3 rounded-lg font-semibold cursor-not-allowed">
                    Please wait...
                  </div>
                )}
              </div>
            </div>

            {/* Link Preview */}
            <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4">
              <p className="text-gray-400 text-sm mb-2">You will be redirected to:</p>
              <p className="text-purple-400 font-mono break-all">{linkData?.originalUrl}</p>
            </div>
          </div>
        </div>

        {/* Montag Ad Network Script */}
        <Script
          src="https://fpyf8.com/88/tag.min.js"
          data-zone="156349"
          async
          data-cfasync="false"
          strategy="afterInteractive"
        />
      </div>
    );
  }

  // Show final redirect page
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      <div className="text-center max-w-md">
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8">
          <h2 className="text-2xl font-bold text-white mb-4">Ready to Redirect!</h2>
          <p className="text-gray-300 mb-6">Click the button below to visit your destination:</p>
          <p className="text-purple-400 font-mono text-sm mb-6 break-all">{linkData?.originalUrl}</p>
          
          <button
            onClick={handleRedirect}
            className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-4 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-105 w-full"
          >
            Visit Link →
          </button>
          
          <Link href="/" className="block mt-4 text-purple-400 hover:text-purple-300 transition-colors">
            ← Back to GameHub
          </Link>
        </div>
      </div>

      {/* Montag Ad Network Script */}
      <Script
        src="https://fpyf8.com/88/tag.min.js"
        data-zone="156349"
        async
        data-cfasync="false"
        strategy="afterInteractive"
      />
    </div>
  );
}
