'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Script from 'next/script';

interface LinkData {
  originalUrl: string;
  clicks: number;
  createdAt: string;
}

interface QuizQuestion {
  question: string;
  options: string[];
  correctAnswer: number;
}

export default function RedirectPage() {
  const params = useParams();
  const shortCode = params.shortCode as string;

  const [linkData, setLinkData] = useState<LinkData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Step management
  const [currentStep, setCurrentStep] = useState(1);
  const [countdown1, setCountdown1] = useState(10);
  const [countdown2, setCountdown2] = useState(15);
  const [hasScrolled, setHasScrolled] = useState(false);
  const [quizAnswer, setQuizAnswer] = useState<number | null>(null);
  const [quizSubmitted, setQuizSubmitted] = useState(false);
  const [quizCountdown, setQuizCountdown] = useState(0);
  const [sessionId] = useState(() => Math.random().toString(36).substring(2, 15));

  // Sample quiz question
  const quizQuestion: QuizQuestion = {
    question: "What is 15 + 27?",
    options: ["40", "42", "44", "46"],
    correctAnswer: 1
  };

  useEffect(() => {
    // Fetch the original URL
    const fetchLinkData = async () => {
      try {
        const response = await fetch(`/api/redirect/${shortCode}`);
        if (!response.ok) {
          throw new Error('Link not found');
        }
        const data = await response.json();
        setLinkData(data);
        setLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Link not found or expired');
        setLoading(false);
      }
    };

    fetchLinkData();
  }, [shortCode]);

  // Step 1: 10 second countdown
  useEffect(() => {
    if (currentStep !== 1 || countdown1 <= 0) return;
    const timer = setInterval(() => {
      setCountdown1(prev => prev <= 1 ? 0 : prev - 1);
    }, 1000);
    return () => clearInterval(timer);
  }, [currentStep, countdown1]);

  // Step 1: 15 second countdown after continue
  useEffect(() => {
    if (currentStep !== 1 || countdown1 > 0 || countdown2 <= 0) return;
    const timer = setInterval(() => {
      setCountdown2(prev => prev <= 1 ? 0 : prev - 1);
    }, 1000);
    return () => clearInterval(timer);
  }, [currentStep, countdown1, countdown2]);

  // Quiz countdown
  useEffect(() => {
    if (!quizSubmitted || quizCountdown <= 0) return;
    const timer = setInterval(() => {
      setQuizCountdown(prev => prev <= 1 ? 0 : prev - 1);
    }, 1000);
    return () => clearInterval(timer);
  }, [quizSubmitted, quizCountdown]);

  // Scroll detection
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setHasScrolled(true);
      }
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleContinueStep1 = () => {
    if (countdown1 > 0) return;
    // Start 15 second countdown
  };

  const handleScrollContinue = () => {
    if (countdown2 > 0 || !hasScrolled) return;
    setCurrentStep(2);
    setHasScrolled(false); // Reset for step 2
  };

  const handleQuizSubmit = () => {
    if (quizAnswer === null) return;
    setQuizSubmitted(true);
    const isCorrect = quizAnswer === quizQuestion.correctAnswer;
    setQuizCountdown(isCorrect ? 10 : 20);
  };

  const handleQuizContinue = () => {
    if (quizCountdown > 0 || !hasScrolled) return;
    setCurrentStep(3);
  };

  const handleFinalRedirect = () => {
    if (linkData) {
      fetch(`/api/redirect/${shortCode}`, { method: 'POST' });
      window.location.href = linkData.originalUrl;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-xl mb-4">{error}</div>
          <Link href="/" className="text-purple-400 hover:text-purple-300">← Back to GameHub</Link>
        </div>
      </div>
    );
  }

  // STEP 1: Gaming Blog Page
  if (currentStep === 1) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <nav className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4">
          <div className="max-w-6xl mx-auto flex justify-between items-center">
            <Link href="/" className="text-2xl font-bold text-white">
              🎮 <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">GameHub</span>
            </Link>
            <div className="text-purple-400 font-mono">Step 1 of 3</div>
          </div>
        </nav>

        <div className="max-w-6xl mx-auto p-6">
          {/* Montag Ad */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-8">
            <h2 className="text-xl font-bold text-white mb-4 text-center">📢 Advertisement</h2>
            <div id="montag-ad-zone-step1" className="min-h-[200px] flex items-center justify-center bg-white/5 rounded-lg">
              <div className="text-gray-400 text-sm">Loading ads...</div>
            </div>
          </div>

          {/* Gaming Blogs */}
          <div className="grid md:grid-cols-2 gap-8 mb-8">
            <article className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <img src="https://images.unsplash.com/photo-1542751371-adc38448a05e?w=800&h=400&fit=crop" alt="Gaming" className="w-full h-48 object-cover rounded-lg mb-4"/>
              <h3 className="text-xl font-bold text-white mb-3">🎮 Top Gaming Trends 2024</h3>
              <p className="text-gray-300 mb-4">Discover the latest gaming trends shaping the industry...</p>
              <div className="text-gray-400 text-sm">The gaming industry continues to evolve with cloud gaming, AI-powered NPCs, and cross-platform gaming leading the charge...</div>
            </article>
            <article className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <img src="https://images.unsplash.com/photo-1560253023-3ec5d502959f?w=800&h=400&fit=crop" alt="Esports" className="w-full h-48 object-cover rounded-lg mb-4"/>
              <h3 className="text-xl font-bold text-white mb-3">🏆 Esports Championship 2024</h3>
              <p className="text-gray-300 mb-4">The biggest esports tournament of the year is here...</p>
              <div className="text-gray-400 text-sm">With prize pools exceeding $10 million, this year's championship features the best teams from around the world...</div>
            </article>
          </div>

          {/* Progress */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 text-center">
            {countdown1 > 0 ? (
              <div>
                <div className="bg-purple-600 text-white rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 text-2xl font-bold">{countdown1}</div>
                <p className="text-gray-300">Please wait {countdown1} seconds...</p>
              </div>
            ) : countdown2 > 0 ? (
              <div>
                <button onClick={handleContinueStep1} className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-4 rounded-lg font-semibold mb-4">Continue →</button>
                <div className="bg-orange-600 text-white rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 text-2xl font-bold">{countdown2}</div>
                <p className="text-gray-300">Please wait {countdown2} more seconds...</p>
              </div>
            ) : !hasScrolled ? (
              <div>
                <p className="text-yellow-400 text-lg mb-4">📜 Please scroll down to continue</p>
                <div className="animate-bounce text-yellow-400 text-2xl">↓</div>
              </div>
            ) : (
              <button onClick={handleScrollContinue} className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-lg font-semibold">Proceed to Quiz →</button>
            )}
          </div>

          <div className="h-96"></div>
          <div className="bg-white/5 rounded-lg p-4">
            <p className="text-gray-400 text-sm mb-2">You will be redirected to:</p>
            <p className="text-purple-400 font-mono break-all">{linkData?.originalUrl}</p>
          </div>
        </div>

        <Script src="https://fpyf8.com/88/tag.min.js" data-zone="156349" async data-cfasync="false" strategy="afterInteractive" />
      </div>
    );
  }

  // STEP 2: Quiz Page
  if (currentStep === 2) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <nav className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20 p-4">
          <div className="max-w-6xl mx-auto flex justify-between items-center">
            <Link href="/" className="text-2xl font-bold text-white">
              🎮 <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">GameHub</span>
            </Link>
            <div className="text-purple-400 font-mono">Step 2 of 3 • Quiz</div>
          </div>
        </nav>

        <div className="max-w-4xl mx-auto p-6">
          {/* Montag Ad */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-8">
            <h2 className="text-xl font-bold text-white mb-4 text-center">📢 Advertisement</h2>
            <div id="montag-ad-zone-step2" className="min-h-[200px] flex items-center justify-center bg-white/5 rounded-lg">
              <div className="text-gray-400 text-sm">Loading ads...</div>
            </div>
          </div>

          {/* Quiz */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-white mb-6 text-center">🧮 Quick Math Challenge</h2>
            <h3 className="text-xl text-white mb-8 text-center">{quizQuestion.question}</h3>

            <div className="grid grid-cols-2 gap-4 max-w-md mx-auto mb-8">
              {quizQuestion.options.map((option, index) => (
                <button
                  key={index}
                  onClick={() => !quizSubmitted && setQuizAnswer(index)}
                  disabled={quizSubmitted}
                  className={`p-4 rounded-lg font-semibold transition-all ${
                    quizSubmitted
                      ? index === quizQuestion.correctAnswer
                        ? 'bg-green-600 text-white'
                        : index === quizAnswer
                        ? 'bg-red-600 text-white'
                        : 'bg-gray-600 text-gray-300'
                      : quizAnswer === index
                      ? 'bg-purple-600 text-white'
                      : 'bg-white/20 text-white hover:bg-white/30'
                  }`}
                >
                  {option}
                </button>
              ))}
            </div>

            {!quizSubmitted && quizAnswer !== null && (
              <div className="text-center">
                <button onClick={handleQuizSubmit} className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold">Submit Answer</button>
              </div>
            )}

            {quizSubmitted && (
              <div className="text-center">
                <div className={`text-2xl font-bold mb-4 ${quizAnswer === quizQuestion.correctAnswer ? 'text-green-400' : 'text-red-400'}`}>
                  {quizAnswer === quizQuestion.correctAnswer ? '✅ Correct!' : '❌ Incorrect!'}
                </div>
                {quizCountdown > 0 ? (
                  <div>
                    <div className={`${quizAnswer === quizQuestion.correctAnswer ? 'bg-green-600' : 'bg-red-600'} text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 text-xl font-bold`}>
                      {quizCountdown}
                    </div>
                    <p className="text-gray-300">Please wait {quizCountdown} seconds...</p>
                  </div>
                ) : !hasScrolled ? (
                  <div>
                    <p className="text-yellow-400 text-lg mb-4">📜 Please scroll down to continue</p>
                    <div className="animate-bounce text-yellow-400 text-2xl">↓</div>
                  </div>
                ) : (
                  <button onClick={handleQuizContinue} className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-4 rounded-lg font-semibold">Get Your Link →</button>
                )}
              </div>
            )}
          </div>

          <div className="h-96"></div>
          <div className="bg-white/5 rounded-lg p-4">
            <p className="text-gray-400 text-sm mb-2">You will be redirected to:</p>
            <p className="text-purple-400 font-mono break-all">{linkData?.originalUrl}</p>
          </div>
        </div>

        <Script src="https://fpyf8.com/88/tag.min.js" data-zone="156349" async data-cfasync="false" strategy="afterInteractive" />
      </div>
    );
  }

  // STEP 3: Final Page
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      <div className="max-w-md w-full">
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 text-center">
          <div className="bg-green-600 text-white rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4 text-3xl">✅</div>
          <h2 className="text-2xl font-bold text-white mb-4">Congratulations!</h2>
          <p className="text-gray-300 mb-6">You've completed all steps successfully!</p>

          <div className="bg-white/5 rounded-lg p-4 mb-6">
            <p className="text-gray-400 text-sm mb-2">Your destination:</p>
            <p className="text-purple-400 font-mono text-sm break-all">{linkData?.originalUrl}</p>
          </div>

          <button
            onClick={handleFinalRedirect}
            className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-4 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all w-full mb-4"
          >
            Visit Your Link →
          </button>

          <Link href="/" className="text-purple-400 hover:text-purple-300 text-sm">← Back to GameHub</Link>
        </div>
      </div>
    </div>
  );
}
}
