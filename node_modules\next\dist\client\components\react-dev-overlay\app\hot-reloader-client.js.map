{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/hot-reloader-client.tsx"], "sourcesContent": ["/// <reference types=\"webpack/module.d.ts\" />\n\nimport type { ReactNode } from 'react'\nimport { useCallback, useEffect, startTransition, useMemo, useRef } from 'react'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport formatWebpackMessages from '../utils/format-webpack-messages'\nimport { useRouter } from '../../navigation'\nimport {\n  ACTION_BEFORE_REFRESH,\n  ACTION_BUILD_ERROR,\n  ACTION_BUILD_OK,\n  ACTION_DEBUG_INFO,\n  ACTION_DEV_INDICATOR,\n  ACTION_REFRESH,\n  ACTION_STATIC_INDICATOR,\n  ACTION_UNHANDLED_ERROR,\n  ACTION_UNHANDLED_REJECTION,\n  ACTION_VERSION_INFO,\n  REACT_REFRESH_FULL_RELOAD,\n  reportInvalidHmrMessage,\n  useErrorOverlayReducer,\n} from '../shared'\nimport { parseStack } from '../utils/parse-stack'\nimport { AppDevOverlay } from './app-dev-overlay'\nimport { useError<PERSON>and<PERSON> } from '../../errors/use-error-handler'\nimport { RuntimeErrorHandler } from '../../errors/runtime-error-handler'\nimport {\n  useSendMessage,\n  useTurbopack,\n  useWebsocket,\n  useWebsocketPing,\n} from '../utils/use-websocket'\nimport { parseComponentStack } from '../utils/parse-component-stack'\nimport type { VersionInfo } from '../../../../server/dev/parse-version-info'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../../../server/dev/hot-reloader-types'\nimport type {\n  HMR_ACTION_TYPES,\n  TurbopackMsgToBrowser,\n} from '../../../../server/dev/hot-reloader-types'\nimport { REACT_REFRESH_FULL_RELOAD_FROM_ERROR } from '../shared'\nimport type { DebugInfo } from '../types'\nimport { useUntrackedPathname } from '../../navigation-untracked'\nimport { getReactStitchedError } from '../../errors/stitched-error'\nimport { handleDevBuildIndicatorHmrEvents } from '../../../dev/dev-build-indicator/internal/handle-dev-build-indicator-hmr-events'\nimport type { GlobalErrorComponent } from '../../error-boundary'\nimport type { DevIndicatorServerState } from '../../../../server/dev/dev-indicator-server-state'\nimport reportHmrLatency from '../utils/report-hmr-latency'\nimport { TurbopackHmr } from '../utils/turbopack-hot-reloader-common'\nimport { NEXT_HMR_REFRESH_HASH_COOKIE } from '../../app-router-headers'\n\nexport interface Dispatcher {\n  onBuildOk(): void\n  onBuildError(message: string): void\n  onVersionInfo(versionInfo: VersionInfo): void\n  onDebugInfo(debugInfo: DebugInfo): void\n  onBeforeRefresh(): void\n  onRefresh(): void\n  onStaticIndicator(status: boolean): void\n  onDevIndicator(devIndicator: DevIndicatorServerState): void\n}\n\nlet mostRecentCompilationHash: any = null\nlet __nextDevClientId = Math.round(Math.random() * 100 + Date.now())\nlet reloading = false\nlet webpackStartMsSinceEpoch: number | null = null\nconst turbopackHmr: TurbopackHmr | null = process.env.TURBOPACK\n  ? new TurbopackHmr()\n  : null\n\nlet pendingHotUpdateWebpack = Promise.resolve()\nlet resolvePendingHotUpdateWebpack: () => void = () => {}\nfunction setPendingHotUpdateWebpack() {\n  pendingHotUpdateWebpack = new Promise((resolve) => {\n    resolvePendingHotUpdateWebpack = () => {\n      resolve()\n    }\n  })\n}\n\nexport function waitForWebpackRuntimeHotUpdate() {\n  return pendingHotUpdateWebpack\n}\n\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash: string) {\n  // Update last known compilation hash.\n  mostRecentCompilationHash = hash\n}\n\n/**\n * Is there a newer version of this code available?\n * For webpack: Check if the hash changed compared to __webpack_hash__\n * For Turbopack: Always true because it doesn't have __webpack_hash__\n */\nfunction isUpdateAvailable() {\n  if (process.env.TURBOPACK) {\n    return true\n  }\n\n  /* globals __webpack_hash__ */\n  // __webpack_hash__ is the hash of the current compilation.\n  // It's a global variable injected by Webpack.\n  return mostRecentCompilationHash !== __webpack_hash__\n}\n\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n  return module.hot.status() === 'idle'\n}\nfunction afterApplyUpdates(fn: any) {\n  if (canApplyUpdates()) {\n    fn()\n  } else {\n    function handler(status: any) {\n      if (status === 'idle') {\n        module.hot.removeStatusHandler(handler)\n        fn()\n      }\n    }\n    module.hot.addStatusHandler(handler)\n  }\n}\n\nfunction performFullReload(err: any, sendMessage: any) {\n  const stackTrace =\n    err &&\n    ((err.stack && err.stack.split('\\n').slice(0, 5).join('\\n')) ||\n      err.message ||\n      err + '')\n\n  sendMessage(\n    JSON.stringify({\n      event: 'client-full-reload',\n      stackTrace,\n      hadRuntimeError: !!RuntimeErrorHandler.hadRuntimeError,\n      dependencyChain: err ? err.dependencyChain : undefined,\n    })\n  )\n\n  if (reloading) return\n  reloading = true\n  window.location.reload()\n}\n\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdatesWebpack(\n  sendMessage: (message: string) => void,\n  dispatcher: Dispatcher\n) {\n  if (!isUpdateAvailable() || !canApplyUpdates()) {\n    resolvePendingHotUpdateWebpack()\n    dispatcher.onBuildOk()\n    reportHmrLatency(sendMessage, [], webpackStartMsSinceEpoch!, Date.now())\n    return\n  }\n\n  function handleApplyUpdates(\n    err: any,\n    updatedModules: (string | number)[] | null\n  ) {\n    if (err || RuntimeErrorHandler.hadRuntimeError || updatedModules == null) {\n      if (err) {\n        console.warn(REACT_REFRESH_FULL_RELOAD)\n      } else if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n      }\n      performFullReload(err, sendMessage)\n      return\n    }\n\n    dispatcher.onBuildOk()\n\n    if (isUpdateAvailable()) {\n      // While we were updating, there was a new update! Do it again.\n      tryApplyUpdatesWebpack(sendMessage, dispatcher)\n      return\n    }\n\n    dispatcher.onRefresh()\n    resolvePendingHotUpdateWebpack()\n    reportHmrLatency(\n      sendMessage,\n      updatedModules,\n      webpackStartMsSinceEpoch!,\n      Date.now()\n    )\n\n    if (process.env.__NEXT_TEST_MODE) {\n      afterApplyUpdates(() => {\n        if (self.__NEXT_HMR_CB) {\n          self.__NEXT_HMR_CB()\n          self.__NEXT_HMR_CB = null\n        }\n      })\n    }\n  }\n\n  // https://webpack.js.org/api/hot-module-replacement/#check\n  module.hot\n    .check(/* autoApply */ false)\n    .then((updatedModules: (string | number)[] | null) => {\n      if (updatedModules == null) {\n        return null\n      }\n\n      // We should always handle an update, even if updatedModules is empty (but\n      // non-null) for any reason. That's what webpack would normally do:\n      // https://github.com/webpack/webpack/blob/3aa6b6bc3a64/lib/hmr/HotModuleReplacement.runtime.js#L296-L298\n      dispatcher.onBeforeRefresh()\n      // https://webpack.js.org/api/hot-module-replacement/#apply\n      return module.hot.apply()\n    })\n    .then(\n      (updatedModules: (string | number)[] | null) => {\n        handleApplyUpdates(null, updatedModules)\n      },\n      (err: any) => {\n        handleApplyUpdates(err, null)\n      }\n    )\n}\n\n/** Handles messages from the server for the App Router. */\nfunction processMessage(\n  obj: HMR_ACTION_TYPES,\n  sendMessage: (message: string) => void,\n  processTurbopackMessage: (msg: TurbopackMsgToBrowser) => void,\n  router: ReturnType<typeof useRouter>,\n  dispatcher: Dispatcher,\n  appIsrManifestRef: ReturnType<typeof useRef>,\n  pathnameRef: ReturnType<typeof useRef>\n) {\n  if (!('action' in obj)) {\n    return\n  }\n\n  function handleErrors(errors: ReadonlyArray<unknown>) {\n    // \"Massage\" webpack messages.\n    const formatted = formatWebpackMessages({\n      errors: errors,\n      warnings: [],\n    })\n\n    // Only show the first error.\n    dispatcher.onBuildError(formatted.errors[0])\n\n    // Also log them to the console.\n    for (let i = 0; i < formatted.errors.length; i++) {\n      console.error(stripAnsi(formatted.errors[i]))\n    }\n\n    // Do not attempt to reload now.\n    // We will reload on next success instead.\n    if (process.env.__NEXT_TEST_MODE) {\n      if (self.__NEXT_HMR_CB) {\n        self.__NEXT_HMR_CB(formatted.errors[0])\n        self.__NEXT_HMR_CB = null\n      }\n    }\n  }\n\n  function handleHotUpdate() {\n    if (process.env.TURBOPACK) {\n      const hmrUpdate = turbopackHmr!.onBuilt()\n      if (hmrUpdate != null) {\n        reportHmrLatency(\n          sendMessage,\n          [...hmrUpdate.updatedModules],\n          hmrUpdate.startMsSinceEpoch,\n          hmrUpdate.endMsSinceEpoch,\n          // suppress the `client-hmr-latency` event if the update was a no-op:\n          hmrUpdate.hasUpdates\n        )\n      }\n      dispatcher.onBuildOk()\n    } else {\n      tryApplyUpdatesWebpack(sendMessage, dispatcher)\n    }\n  }\n\n  switch (obj.action) {\n    case HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST: {\n      if (process.env.__NEXT_DEV_INDICATOR) {\n        if (appIsrManifestRef) {\n          appIsrManifestRef.current = obj.data\n\n          // handle initial status on receiving manifest\n          // navigation is handled in useEffect for pathname changes\n          // as we'll receive the updated manifest before usePathname\n          // triggers for new value\n          if ((pathnameRef.current as string) in obj.data) {\n            dispatcher.onStaticIndicator(true)\n          } else {\n            dispatcher.onStaticIndicator(false)\n          }\n        }\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILDING: {\n      if (process.env.TURBOPACK) {\n        turbopackHmr!.onBuilding()\n      } else {\n        webpackStartMsSinceEpoch = Date.now()\n        setPendingHotUpdateWebpack()\n        console.log('[Fast Refresh] rebuilding')\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n    case HMR_ACTIONS_SENT_TO_BROWSER.SYNC: {\n      if (obj.hash) {\n        handleAvailableHash(obj.hash)\n      }\n\n      const { errors, warnings } = obj\n\n      // Is undefined when it's a 'built' event\n      if ('versionInfo' in obj) dispatcher.onVersionInfo(obj.versionInfo)\n      if ('debug' in obj && obj.debug) dispatcher.onDebugInfo(obj.debug)\n      if ('devIndicator' in obj) dispatcher.onDevIndicator(obj.devIndicator)\n\n      const hasErrors = Boolean(errors && errors.length)\n      // Compilation with errors (e.g. syntax error or missing modules).\n      if (hasErrors) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-error',\n            errorCount: errors.length,\n            clientId: __nextDevClientId,\n          })\n        )\n\n        handleErrors(errors)\n        return\n      }\n\n      const hasWarnings = Boolean(warnings && warnings.length)\n      if (hasWarnings) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-warning',\n            warningCount: warnings.length,\n            clientId: __nextDevClientId,\n          })\n        )\n\n        // Print warnings to the console.\n        const formattedMessages = formatWebpackMessages({\n          warnings: warnings,\n          errors: [],\n        })\n\n        for (let i = 0; i < formattedMessages.warnings.length; i++) {\n          if (i === 5) {\n            console.warn(\n              'There were more warnings in other files.\\n' +\n                'You can find a complete log in the terminal.'\n            )\n            break\n          }\n          console.warn(stripAnsi(formattedMessages.warnings[i]))\n        }\n\n        // No early return here as we need to apply modules in the same way between warnings only and compiles without warnings\n      }\n\n      sendMessage(\n        JSON.stringify({\n          event: 'client-success',\n          clientId: __nextDevClientId,\n        })\n      )\n\n      if (obj.action === HMR_ACTIONS_SENT_TO_BROWSER.BUILT) {\n        handleHotUpdate()\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED: {\n      processTurbopackMessage({\n        type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n        data: {\n          sessionId: obj.data.sessionId,\n        },\n      })\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE: {\n      turbopackHmr!.onTurbopackMessage(obj)\n      dispatcher.onBeforeRefresh()\n      processTurbopackMessage({\n        type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n        data: obj.data,\n      })\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n        performFullReload(null, sendMessage)\n      }\n      dispatcher.onRefresh()\n      break\n    }\n    // TODO-APP: make server component change more granular\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES: {\n      turbopackHmr?.onServerComponentChanges()\n      sendMessage(\n        JSON.stringify({\n          event: 'server-component-reload-page',\n          clientId: __nextDevClientId,\n          hash: obj.hash,\n        })\n      )\n\n      // Store the latest hash in a session cookie so that it's sent back to the\n      // server with any subsequent requests.\n      document.cookie = `${NEXT_HMR_REFRESH_HASH_COOKIE}=${obj.hash}`\n\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        if (reloading) return\n        reloading = true\n        return window.location.reload()\n      }\n\n      startTransition(() => {\n        router.hmrRefresh()\n        dispatcher.onRefresh()\n      })\n\n      if (process.env.__NEXT_TEST_MODE) {\n        if (self.__NEXT_HMR_CB) {\n          self.__NEXT_HMR_CB()\n          self.__NEXT_HMR_CB = null\n        }\n      }\n\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE: {\n      turbopackHmr?.onReloadPage()\n      sendMessage(\n        JSON.stringify({\n          event: 'client-reload-page',\n          clientId: __nextDevClientId,\n        })\n      )\n      if (reloading) return\n      reloading = true\n      return window.location.reload()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE:\n    case HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE: {\n      turbopackHmr?.onPageAddRemove()\n      // TODO-APP: potentially only refresh if the currently viewed page was added/removed.\n      return router.hmrRefresh()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR: {\n      const { errorJSON } = obj\n      if (errorJSON) {\n        const { message, stack } = JSON.parse(errorJSON)\n        const error = new Error(message)\n        error.stack = stack\n        handleErrors([error])\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE: {\n      return\n    }\n    default: {\n    }\n  }\n}\n\nexport default function HotReload({\n  assetPrefix,\n  children,\n  globalError,\n}: {\n  assetPrefix: string\n  children: ReactNode\n  globalError: [GlobalErrorComponent, React.ReactNode]\n}) {\n  const [state, dispatch] = useErrorOverlayReducer('app')\n\n  const dispatcher = useMemo<Dispatcher>(() => {\n    return {\n      onBuildOk() {\n        dispatch({ type: ACTION_BUILD_OK })\n      },\n      onBuildError(message) {\n        dispatch({ type: ACTION_BUILD_ERROR, message })\n      },\n      onBeforeRefresh() {\n        dispatch({ type: ACTION_BEFORE_REFRESH })\n      },\n      onRefresh() {\n        dispatch({ type: ACTION_REFRESH })\n      },\n      onVersionInfo(versionInfo) {\n        dispatch({ type: ACTION_VERSION_INFO, versionInfo })\n      },\n      onStaticIndicator(status: boolean) {\n        dispatch({ type: ACTION_STATIC_INDICATOR, staticIndicator: status })\n      },\n      onDebugInfo(debugInfo) {\n        dispatch({ type: ACTION_DEBUG_INFO, debugInfo })\n      },\n      onDevIndicator(devIndicator) {\n        dispatch({\n          type: ACTION_DEV_INDICATOR,\n          devIndicator,\n        })\n      },\n    }\n  }, [dispatch])\n\n  const handleOnUnhandledError = useCallback(\n    (error: Error): void => {\n      // Component stack is added to the error in use-error-handler in case there was a hydration error\n      const componentStackTrace = (error as any)._componentStack\n\n      dispatch({\n        type: ACTION_UNHANDLED_ERROR,\n        reason: error,\n        frames: parseStack(error.stack || ''),\n        componentStackFrames:\n          typeof componentStackTrace === 'string'\n            ? parseComponentStack(componentStackTrace)\n            : undefined,\n      })\n    },\n    [dispatch]\n  )\n\n  const handleOnUnhandledRejection = useCallback(\n    (reason: Error): void => {\n      const stitchedError = getReactStitchedError(reason)\n      dispatch({\n        type: ACTION_UNHANDLED_REJECTION,\n        reason: stitchedError,\n        frames: parseStack(stitchedError.stack || ''),\n      })\n    },\n    [dispatch]\n  )\n  useErrorHandler(handleOnUnhandledError, handleOnUnhandledRejection)\n\n  const webSocketRef = useWebsocket(assetPrefix)\n  useWebsocketPing(webSocketRef)\n  const sendMessage = useSendMessage(webSocketRef)\n  const processTurbopackMessage = useTurbopack(sendMessage, (err) =>\n    performFullReload(err, sendMessage)\n  )\n\n  const router = useRouter()\n\n  // We don't want access of the pathname for the dev tools to trigger a dynamic\n  // access (as the dev overlay will never be present in production).\n  const pathname = useUntrackedPathname()\n  const appIsrManifestRef = useRef<Record<string, false | number>>({})\n  const pathnameRef = useRef(pathname)\n\n  if (process.env.__NEXT_DEV_INDICATOR) {\n    // this conditional is only for dead-code elimination which\n    // isn't a runtime conditional only build-time so ignore hooks rule\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      pathnameRef.current = pathname\n\n      const appIsrManifest = appIsrManifestRef.current\n\n      if (appIsrManifest) {\n        if (pathname && pathname in appIsrManifest) {\n          try {\n            dispatcher.onStaticIndicator(true)\n          } catch (reason) {\n            let message = ''\n\n            if (reason instanceof DOMException) {\n              // Most likely a SecurityError, because of an unavailable localStorage\n              message = reason.stack ?? reason.message\n            } else if (reason instanceof Error) {\n              message = 'Error: ' + reason.message + '\\n' + (reason.stack ?? '')\n            } else {\n              message = 'Unexpected Exception: ' + reason\n            }\n\n            console.warn('[HMR] ' + message)\n          }\n        } else {\n          dispatcher.onStaticIndicator(false)\n        }\n      }\n    }, [pathname, dispatcher])\n  }\n\n  useEffect(() => {\n    const websocket = webSocketRef.current\n    if (!websocket) return\n\n    const handler = (event: MessageEvent<any>) => {\n      try {\n        const obj = JSON.parse(event.data)\n        handleDevBuildIndicatorHmrEvents(obj)\n        processMessage(\n          obj,\n          sendMessage,\n          processTurbopackMessage,\n          router,\n          dispatcher,\n          appIsrManifestRef,\n          pathnameRef\n        )\n      } catch (err: unknown) {\n        reportInvalidHmrMessage(event, err)\n      }\n    }\n\n    websocket.addEventListener('message', handler)\n    return () => websocket.removeEventListener('message', handler)\n  }, [\n    sendMessage,\n    router,\n    webSocketRef,\n    dispatcher,\n    processTurbopackMessage,\n    appIsrManifestRef,\n  ])\n\n  return (\n    <AppDevOverlay state={state} globalError={globalError}>\n      {children}\n    </AppDevOverlay>\n  )\n}\n"], "names": ["HotReload", "waitForWebpackRuntimeHotUpdate", "mostRecentCompilationHash", "__nextDevClientId", "Math", "round", "random", "Date", "now", "reloading", "webpackStartMsSinceEpoch", "turbopackHmr", "process", "env", "TURBOPACK", "TurbopackHmr", "pendingHotUpdateWebpack", "Promise", "resolve", "resolvePendingHotUpdateWebpack", "setPendingHotUpdateWebpack", "handleAvailableHash", "hash", "isUpdateAvailable", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "handler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "performFullReload", "err", "sendMessage", "stackTrace", "stack", "split", "slice", "join", "message", "JSON", "stringify", "event", "hadRuntimeError", "RuntimeError<PERSON>andler", "dependency<PERSON><PERSON>n", "undefined", "window", "location", "reload", "tryApplyUpdatesWebpack", "dispatcher", "onBuildOk", "reportHmrLatency", "handleApplyUpdates", "updatedModules", "console", "warn", "REACT_REFRESH_FULL_RELOAD", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "onRefresh", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "check", "then", "onBeforeRefresh", "apply", "processMessage", "obj", "processTurbopackMessage", "router", "appIsrManifestRef", "pathnameRef", "handleErrors", "errors", "formatted", "formatWebpackMessages", "warnings", "onBuildError", "i", "length", "error", "stripAnsi", "handleHotUpdate", "hmrUpdate", "onBuilt", "startMsSinceEpoch", "endMsSinceEpoch", "hasUpdates", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "ISR_MANIFEST", "__NEXT_DEV_INDICATOR", "current", "data", "onStaticIndicator", "BUILDING", "onBuilding", "log", "BUILT", "SYNC", "onVersionInfo", "versionInfo", "debug", "onDebugInfo", "onDevIndicator", "devIndicator", "hasErrors", "Boolean", "errorCount", "clientId", "hasWarnings", "warningCount", "formattedMessages", "TURBOPACK_CONNECTED", "type", "sessionId", "TURBOPACK_MESSAGE", "onTurbopackMessage", "SERVER_COMPONENT_CHANGES", "onServerComponentChanges", "document", "cookie", "NEXT_HMR_REFRESH_HASH_COOKIE", "startTransition", "hmrRefresh", "RELOAD_PAGE", "onReloadPage", "ADDED_PAGE", "REMOVED_PAGE", "onPageAddRemove", "SERVER_ERROR", "errorJSON", "parse", "Error", "DEV_PAGES_MANIFEST_UPDATE", "assetPrefix", "children", "globalError", "state", "dispatch", "useErrorOverlayReducer", "useMemo", "ACTION_BUILD_OK", "ACTION_BUILD_ERROR", "ACTION_BEFORE_REFRESH", "ACTION_REFRESH", "ACTION_VERSION_INFO", "ACTION_STATIC_INDICATOR", "staticIndicator", "debugInfo", "ACTION_DEBUG_INFO", "ACTION_DEV_INDICATOR", "handleOnUnhandledError", "useCallback", "componentStackTrace", "_componentStack", "ACTION_UNHANDLED_ERROR", "reason", "frames", "parseStack", "componentStackFrames", "parseComponentStack", "handleOnUnhandledRejection", "stitchedError", "getReactStitchedError", "ACTION_UNHANDLED_REJECTION", "useErrorHandler", "webSocketRef", "useWebsocket", "useWebsocketPing", "useSendMessage", "useTurbopack", "useRouter", "pathname", "useUntrackedPathname", "useRef", "useEffect", "appIsrManifest", "DOMException", "websocket", "handleDevBuildIndicatorHmrEvents", "reportInvalidHmrMessage", "addEventListener", "removeEventListener", "AppDevOverlay"], "mappings": "AAAA,6CAA6C;;;;;;;;;;;;;;;;IAyd7C,OAiKC;eAjKuBA;;IA1YRC,8BAA8B;eAA9BA;;;;;uBA5EyD;oEACnD;gFACY;4BACR;wBAenB;4BACoB;+BACG;iCACE;qCACI;8BAM7B;qCAC6B;kCAEQ;qCAOP;+BACC;kDACW;2EAGpB;4CACA;kCACgB;AAa7C,IAAIC,4BAAiC;AACrC,IAAIC,oBAAoBC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AACjE,IAAIC,YAAY;AAChB,IAAIC,2BAA0C;AAC9C,MAAMC,eAAoCC,QAAQC,GAAG,CAACC,SAAS,GAC3D,IAAIC,wCAAY,KAChB;AAEJ,IAAIC,0BAA0BC,QAAQC,OAAO;AAC7C,IAAIC,iCAA6C,KAAO;AACxD,SAASC;IACPJ,0BAA0B,IAAIC,QAAQ,CAACC;QACrCC,iCAAiC;YAC/BD;QACF;IACF;AACF;AAEO,SAASjB;IACd,OAAOe;AACT;AAEA,kDAAkD;AAClD,SAASK,oBAAoBC,IAAY;IACvC,sCAAsC;IACtCpB,4BAA4BoB;AAC9B;AAEA;;;;CAIC,GACD,SAASC;IACP,IAAIX,QAAQC,GAAG,CAACC,SAAS,EAAE;QACzB,OAAO;IACT;IAEA,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOZ,8BAA8BsB;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAO;IAChC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAASC,QAAQH,MAAW;YAC1B,IAAIA,WAAW,QAAQ;gBACrBF,OAAOC,GAAG,CAACK,mBAAmB,CAACD;gBAC/BD;YACF;QACF;QACAJ,OAAOC,GAAG,CAACM,gBAAgB,CAACF;IAC9B;AACF;AAEA,SAASG,kBAAkBC,GAAQ,EAAEC,WAAgB;IACnD,MAAMC,aACJF,OACC,CAAA,AAACA,IAAIG,KAAK,IAAIH,IAAIG,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDN,IAAIO,OAAO,IACXP,MAAM,EAAC;IAEXC,YACEO,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPR;QACAS,iBAAiB,CAAC,CAACC,wCAAmB,CAACD,eAAe;QACtDE,iBAAiBb,MAAMA,IAAIa,eAAe,GAAGC;IAC/C;IAGF,IAAIxC,WAAW;IACfA,YAAY;IACZyC,OAAOC,QAAQ,CAACC,MAAM;AACxB;AAEA,iEAAiE;AACjE,SAASC,uBACPjB,WAAsC,EACtCkB,UAAsB;IAEtB,IAAI,CAAC/B,uBAAuB,CAACE,mBAAmB;QAC9CN;QACAmC,WAAWC,SAAS;QACpBC,IAAAA,yBAAgB,EAACpB,aAAa,EAAE,EAAE1B,0BAA2BH,KAAKC,GAAG;QACrE;IACF;IAEA,SAASiD,mBACPtB,GAAQ,EACRuB,cAA0C;QAE1C,IAAIvB,OAAOY,wCAAmB,CAACD,eAAe,IAAIY,kBAAkB,MAAM;YACxE,IAAIvB,KAAK;gBACPwB,QAAQC,IAAI,CAACC,iCAAyB;YACxC,OAAO,IAAId,wCAAmB,CAACD,eAAe,EAAE;gBAC9Ca,QAAQC,IAAI,CAACE,4CAAoC;YACnD;YACA5B,kBAAkBC,KAAKC;YACvB;QACF;QAEAkB,WAAWC,SAAS;QAEpB,IAAIhC,qBAAqB;YACvB,+DAA+D;YAC/D8B,uBAAuBjB,aAAakB;YACpC;QACF;QAEAA,WAAWS,SAAS;QACpB5C;QACAqC,IAAAA,yBAAgB,EACdpB,aACAsB,gBACAhD,0BACAH,KAAKC,GAAG;QAGV,IAAII,QAAQC,GAAG,CAACmD,gBAAgB,EAAE;YAChCnC,kBAAkB;gBAChB,IAAIoC,KAAKC,aAAa,EAAE;oBACtBD,KAAKC,aAAa;oBAClBD,KAAKC,aAAa,GAAG;gBACvB;YACF;QACF;IACF;IAEA,2DAA2D;IAC3DxC,OAAOC,GAAG,CACPwC,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAACV;QACL,IAAIA,kBAAkB,MAAM;YAC1B,OAAO;QACT;QAEA,0EAA0E;QAC1E,mEAAmE;QACnE,yGAAyG;QACzGJ,WAAWe,eAAe;QAC1B,2DAA2D;QAC3D,OAAO3C,OAAOC,GAAG,CAAC2C,KAAK;IACzB,GACCF,IAAI,CACH,CAACV;QACCD,mBAAmB,MAAMC;IAC3B,GACA,CAACvB;QACCsB,mBAAmBtB,KAAK;IAC1B;AAEN;AAEA,yDAAyD,GACzD,SAASoC,eACPC,GAAqB,EACrBpC,WAAsC,EACtCqC,uBAA6D,EAC7DC,MAAoC,EACpCpB,UAAsB,EACtBqB,iBAA4C,EAC5CC,WAAsC;IAEtC,IAAI,CAAE,CAAA,YAAYJ,GAAE,GAAI;QACtB;IACF;IAEA,SAASK,aAAaC,MAA8B;QAClD,8BAA8B;QAC9B,MAAMC,YAAYC,IAAAA,8BAAqB,EAAC;YACtCF,QAAQA;YACRG,UAAU,EAAE;QACd;QAEA,6BAA6B;QAC7B3B,WAAW4B,YAAY,CAACH,UAAUD,MAAM,CAAC,EAAE;QAE3C,gCAAgC;QAChC,IAAK,IAAIK,IAAI,GAAGA,IAAIJ,UAAUD,MAAM,CAACM,MAAM,EAAED,IAAK;YAChDxB,QAAQ0B,KAAK,CAACC,IAAAA,kBAAS,EAACP,UAAUD,MAAM,CAACK,EAAE;QAC7C;QAEA,gCAAgC;QAChC,0CAA0C;QAC1C,IAAIvE,QAAQC,GAAG,CAACmD,gBAAgB,EAAE;YAChC,IAAIC,KAAKC,aAAa,EAAE;gBACtBD,KAAKC,aAAa,CAACa,UAAUD,MAAM,CAAC,EAAE;gBACtCb,KAAKC,aAAa,GAAG;YACvB;QACF;IACF;IAEA,SAASqB;QACP,IAAI3E,QAAQC,GAAG,CAACC,SAAS,EAAE;YACzB,MAAM0E,YAAY7E,aAAc8E,OAAO;YACvC,IAAID,aAAa,MAAM;gBACrBhC,IAAAA,yBAAgB,EACdpB,aACA;uBAAIoD,UAAU9B,cAAc;iBAAC,EAC7B8B,UAAUE,iBAAiB,EAC3BF,UAAUG,eAAe,EACzB,qEAAqE;gBACrEH,UAAUI,UAAU;YAExB;YACAtC,WAAWC,SAAS;QACtB,OAAO;YACLF,uBAAuBjB,aAAakB;QACtC;IACF;IAEA,OAAQkB,IAAIqB,MAAM;QAChB,KAAKC,6CAA2B,CAACC,YAAY;YAAE;gBAC7C,IAAInF,QAAQC,GAAG,CAACmF,oBAAoB,EAAE;oBACpC,IAAIrB,mBAAmB;wBACrBA,kBAAkBsB,OAAO,GAAGzB,IAAI0B,IAAI;wBAEpC,8CAA8C;wBAC9C,0DAA0D;wBAC1D,2DAA2D;wBAC3D,yBAAyB;wBACzB,IAAI,AAACtB,YAAYqB,OAAO,IAAezB,IAAI0B,IAAI,EAAE;4BAC/C5C,WAAW6C,iBAAiB,CAAC;wBAC/B,OAAO;4BACL7C,WAAW6C,iBAAiB,CAAC;wBAC/B;oBACF;gBACF;gBACA;YACF;QACA,KAAKL,6CAA2B,CAACM,QAAQ;YAAE;gBACzC,IAAIxF,QAAQC,GAAG,CAACC,SAAS,EAAE;oBACzBH,aAAc0F,UAAU;gBAC1B,OAAO;oBACL3F,2BAA2BH,KAAKC,GAAG;oBACnCY;oBACAuC,QAAQ2C,GAAG,CAAC;gBACd;gBACA;YACF;QACA,KAAKR,6CAA2B,CAACS,KAAK;QACtC,KAAKT,6CAA2B,CAACU,IAAI;YAAE;gBACrC,IAAIhC,IAAIlD,IAAI,EAAE;oBACZD,oBAAoBmD,IAAIlD,IAAI;gBAC9B;gBAEA,MAAM,EAAEwD,MAAM,EAAEG,QAAQ,EAAE,GAAGT;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAKlB,WAAWmD,aAAa,CAACjC,IAAIkC,WAAW;gBAClE,IAAI,WAAWlC,OAAOA,IAAImC,KAAK,EAAErD,WAAWsD,WAAW,CAACpC,IAAImC,KAAK;gBACjE,IAAI,kBAAkBnC,KAAKlB,WAAWuD,cAAc,CAACrC,IAAIsC,YAAY;gBAErE,MAAMC,YAAYC,QAAQlC,UAAUA,OAAOM,MAAM;gBACjD,kEAAkE;gBAClE,IAAI2B,WAAW;oBACb3E,YACEO,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPoE,YAAYnC,OAAOM,MAAM;wBACzB8B,UAAU/G;oBACZ;oBAGF0E,aAAaC;oBACb;gBACF;gBAEA,MAAMqC,cAAcH,QAAQ/B,YAAYA,SAASG,MAAM;gBACvD,IAAI+B,aAAa;oBACf/E,YACEO,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPuE,cAAcnC,SAASG,MAAM;wBAC7B8B,UAAU/G;oBACZ;oBAGF,iCAAiC;oBACjC,MAAMkH,oBAAoBrC,IAAAA,8BAAqB,EAAC;wBAC9CC,UAAUA;wBACVH,QAAQ,EAAE;oBACZ;oBAEA,IAAK,IAAIK,IAAI,GAAGA,IAAIkC,kBAAkBpC,QAAQ,CAACG,MAAM,EAAED,IAAK;wBAC1D,IAAIA,MAAM,GAAG;4BACXxB,QAAQC,IAAI,CACV,+CACE;4BAEJ;wBACF;wBACAD,QAAQC,IAAI,CAAC0B,IAAAA,kBAAS,EAAC+B,kBAAkBpC,QAAQ,CAACE,EAAE;oBACtD;gBAEA,uHAAuH;gBACzH;gBAEA/C,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPqE,UAAU/G;gBACZ;gBAGF,IAAIqE,IAAIqB,MAAM,KAAKC,6CAA2B,CAACS,KAAK,EAAE;oBACpDhB;gBACF;gBACA;YACF;QACA,KAAKO,6CAA2B,CAACwB,mBAAmB;YAAE;gBACpD7C,wBAAwB;oBACtB8C,MAAMzB,6CAA2B,CAACwB,mBAAmB;oBACrDpB,MAAM;wBACJsB,WAAWhD,IAAI0B,IAAI,CAACsB,SAAS;oBAC/B;gBACF;gBACA;YACF;QACA,KAAK1B,6CAA2B,CAAC2B,iBAAiB;YAAE;gBAClD9G,aAAc+G,kBAAkB,CAAClD;gBACjClB,WAAWe,eAAe;gBAC1BI,wBAAwB;oBACtB8C,MAAMzB,6CAA2B,CAAC2B,iBAAiB;oBACnDvB,MAAM1B,IAAI0B,IAAI;gBAChB;gBACA,IAAInD,wCAAmB,CAACD,eAAe,EAAE;oBACvCa,QAAQC,IAAI,CAACE,4CAAoC;oBACjD5B,kBAAkB,MAAME;gBAC1B;gBACAkB,WAAWS,SAAS;gBACpB;YACF;QACA,uDAAuD;QACvD,KAAK+B,6CAA2B,CAAC6B,wBAAwB;YAAE;gBACzDhH,gCAAAA,aAAciH,wBAAwB;gBACtCxF,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPqE,UAAU/G;oBACVmB,MAAMkD,IAAIlD,IAAI;gBAChB;gBAGF,0EAA0E;gBAC1E,uCAAuC;gBACvCuG,SAASC,MAAM,GAAG,AAAGC,8CAA4B,GAAC,MAAGvD,IAAIlD,IAAI;gBAE7D,IAAIyB,wCAAmB,CAACD,eAAe,EAAE;oBACvC,IAAIrC,WAAW;oBACfA,YAAY;oBACZ,OAAOyC,OAAOC,QAAQ,CAACC,MAAM;gBAC/B;gBAEA4E,IAAAA,sBAAe,EAAC;oBACdtD,OAAOuD,UAAU;oBACjB3E,WAAWS,SAAS;gBACtB;gBAEA,IAAInD,QAAQC,GAAG,CAACmD,gBAAgB,EAAE;oBAChC,IAAIC,KAAKC,aAAa,EAAE;wBACtBD,KAAKC,aAAa;wBAClBD,KAAKC,aAAa,GAAG;oBACvB;gBACF;gBAEA;YACF;QACA,KAAK4B,6CAA2B,CAACoC,WAAW;YAAE;gBAC5CvH,gCAAAA,aAAcwH,YAAY;gBAC1B/F,YACEO,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPqE,UAAU/G;gBACZ;gBAEF,IAAIM,WAAW;gBACfA,YAAY;gBACZ,OAAOyC,OAAOC,QAAQ,CAACC,MAAM;YAC/B;QACA,KAAK0C,6CAA2B,CAACsC,UAAU;QAC3C,KAAKtC,6CAA2B,CAACuC,YAAY;YAAE;gBAC7C1H,gCAAAA,aAAc2H,eAAe;gBAC7B,qFAAqF;gBACrF,OAAO5D,OAAOuD,UAAU;YAC1B;QACA,KAAKnC,6CAA2B,CAACyC,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGhE;gBACtB,IAAIgE,WAAW;oBACb,MAAM,EAAE9F,OAAO,EAAEJ,KAAK,EAAE,GAAGK,KAAK8F,KAAK,CAACD;oBACtC,MAAMnD,QAAQ,qBAAkB,CAAlB,IAAIqD,MAAMhG,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB;oBAC/B2C,MAAM/C,KAAK,GAAGA;oBACduC,aAAa;wBAACQ;qBAAM;gBACtB;gBACA;YACF;QACA,KAAKS,6CAA2B,CAAC6C,yBAAyB;YAAE;gBAC1D;YACF;QACA;YAAS,CACT;IACF;AACF;AAEe,SAAS3I,UAAU,KAQjC;IARiC,IAAA,EAChC4I,WAAW,EACXC,QAAQ,EACRC,WAAW,EAKZ,GARiC;IAShC,MAAM,CAACC,OAAOC,SAAS,GAAGC,IAAAA,8BAAsB,EAAC;IAEjD,MAAM3F,aAAa4F,IAAAA,cAAO,EAAa;QACrC,OAAO;YACL3F;gBACEyF,SAAS;oBAAEzB,MAAM4B,uBAAe;gBAAC;YACnC;YACAjE,cAAaxC,OAAO;gBAClBsG,SAAS;oBAAEzB,MAAM6B,0BAAkB;oBAAE1G;gBAAQ;YAC/C;YACA2B;gBACE2E,SAAS;oBAAEzB,MAAM8B,6BAAqB;gBAAC;YACzC;YACAtF;gBACEiF,SAAS;oBAAEzB,MAAM+B,sBAAc;gBAAC;YAClC;YACA7C,eAAcC,WAAW;gBACvBsC,SAAS;oBAAEzB,MAAMgC,2BAAmB;oBAAE7C;gBAAY;YACpD;YACAP,mBAAkBvE,MAAe;gBAC/BoH,SAAS;oBAAEzB,MAAMiC,+BAAuB;oBAAEC,iBAAiB7H;gBAAO;YACpE;YACAgF,aAAY8C,SAAS;gBACnBV,SAAS;oBAAEzB,MAAMoC,yBAAiB;oBAAED;gBAAU;YAChD;YACA7C,gBAAeC,YAAY;gBACzBkC,SAAS;oBACPzB,MAAMqC,4BAAoB;oBAC1B9C;gBACF;YACF;QACF;IACF,GAAG;QAACkC;KAAS;IAEb,MAAMa,yBAAyBC,IAAAA,kBAAW,EACxC,CAACzE;QACC,iGAAiG;QACjG,MAAM0E,sBAAsB,AAAC1E,MAAc2E,eAAe;QAE1DhB,SAAS;YACPzB,MAAM0C,8BAAsB;YAC5BC,QAAQ7E;YACR8E,QAAQC,IAAAA,sBAAU,EAAC/E,MAAM/C,KAAK,IAAI;YAClC+H,sBACE,OAAON,wBAAwB,WAC3BO,IAAAA,wCAAmB,EAACP,uBACpB9G;QACR;IACF,GACA;QAAC+F;KAAS;IAGZ,MAAMuB,6BAA6BT,IAAAA,kBAAW,EAC5C,CAACI;QACC,MAAMM,gBAAgBC,IAAAA,oCAAqB,EAACP;QAC5ClB,SAAS;YACPzB,MAAMmD,kCAA0B;YAChCR,QAAQM;YACRL,QAAQC,IAAAA,sBAAU,EAACI,cAAclI,KAAK,IAAI;QAC5C;IACF,GACA;QAAC0G;KAAS;IAEZ2B,IAAAA,gCAAe,EAACd,wBAAwBU;IAExC,MAAMK,eAAeC,IAAAA,0BAAY,EAACjC;IAClCkC,IAAAA,8BAAgB,EAACF;IACjB,MAAMxI,cAAc2I,IAAAA,4BAAc,EAACH;IACnC,MAAMnG,0BAA0BuG,IAAAA,0BAAY,EAAC5I,aAAa,CAACD,MACzDD,kBAAkBC,KAAKC;IAGzB,MAAMsC,SAASuG,IAAAA,qBAAS;IAExB,8EAA8E;IAC9E,mEAAmE;IACnE,MAAMC,WAAWC,IAAAA,yCAAoB;IACrC,MAAMxG,oBAAoByG,IAAAA,aAAM,EAAiC,CAAC;IAClE,MAAMxG,cAAcwG,IAAAA,aAAM,EAACF;IAE3B,IAAItK,QAAQC,GAAG,CAACmF,oBAAoB,EAAE;QACpC,2DAA2D;QAC3D,mEAAmE;QACnE,sDAAsD;QACtDqF,IAAAA,gBAAS,EAAC;YACRzG,YAAYqB,OAAO,GAAGiF;YAEtB,MAAMI,iBAAiB3G,kBAAkBsB,OAAO;YAEhD,IAAIqF,gBAAgB;gBAClB,IAAIJ,YAAYA,YAAYI,gBAAgB;oBAC1C,IAAI;wBACFhI,WAAW6C,iBAAiB,CAAC;oBAC/B,EAAE,OAAO+D,QAAQ;wBACf,IAAIxH,UAAU;wBAEd,IAAIwH,kBAAkBqB,cAAc;gCAExBrB;4BADV,sEAAsE;4BACtExH,UAAUwH,CAAAA,gBAAAA,OAAO5H,KAAK,YAAZ4H,gBAAgBA,OAAOxH,OAAO;wBAC1C,OAAO,IAAIwH,kBAAkBxB,OAAO;gCACawB;4BAA/CxH,UAAU,YAAYwH,OAAOxH,OAAO,GAAG,OAAQwH,CAAAA,CAAAA,iBAAAA,OAAO5H,KAAK,YAAZ4H,iBAAgB,EAAC;wBAClE,OAAO;4BACLxH,UAAU,2BAA2BwH;wBACvC;wBAEAvG,QAAQC,IAAI,CAAC,WAAWlB;oBAC1B;gBACF,OAAO;oBACLY,WAAW6C,iBAAiB,CAAC;gBAC/B;YACF;QACF,GAAG;YAAC+E;YAAU5H;SAAW;IAC3B;IAEA+H,IAAAA,gBAAS,EAAC;QACR,MAAMG,YAAYZ,aAAa3E,OAAO;QACtC,IAAI,CAACuF,WAAW;QAEhB,MAAMzJ,UAAU,CAACc;YACf,IAAI;gBACF,MAAM2B,MAAM7B,KAAK8F,KAAK,CAAC5F,MAAMqD,IAAI;gBACjCuF,IAAAA,kEAAgC,EAACjH;gBACjCD,eACEC,KACApC,aACAqC,yBACAC,QACApB,YACAqB,mBACAC;YAEJ,EAAE,OAAOzC,KAAc;gBACrBuJ,IAAAA,+BAAuB,EAAC7I,OAAOV;YACjC;QACF;QAEAqJ,UAAUG,gBAAgB,CAAC,WAAW5J;QACtC,OAAO,IAAMyJ,UAAUI,mBAAmB,CAAC,WAAW7J;IACxD,GAAG;QACDK;QACAsC;QACAkG;QACAtH;QACAmB;QACAE;KACD;IAED,qBACE,qBAACkH,4BAAa;QAAC9C,OAAOA;QAAOD,aAAaA;kBACvCD;;AAGP"}