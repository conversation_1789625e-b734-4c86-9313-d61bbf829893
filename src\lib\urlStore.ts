// Simple in-memory store for URLs (in production, use a database)
export interface UrlData {
  originalUrl: string;
  clicks: number;
  createdAt: Date;
}

class UrlStore {
  private store = new Map<string, UrlData>();

  constructor() {
    // Initialize with some demo data
    this.store.set('abc123', {
      originalUrl: 'https://store.steampowered.com/app/1091500/Cyberpunk_2077/',
      clicks: 1234,
      createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1 day ago
    });
    
    this.store.set('xyz789', {
      originalUrl: 'https://www.epicgames.com/store/en-US/p/fortnite',
      clicks: 856,
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2 days ago
    });
  }

  set(shortCode: string, data: UrlData): void {
    this.store.set(shortCode, data);
  }

  get(shortCode: string): UrlData | undefined {
    return this.store.get(shortCode);
  }

  has(shortCode: string): boolean {
    return this.store.has(shortCode);
  }

  getAll(): Array<{ shortCode: string } & UrlData> {
    return Array.from(this.store.entries()).map(([shortCode, data]) => ({
      shortCode,
      ...data
    }));
  }

  incrementClicks(shortCode: string): boolean {
    const data = this.store.get(shortCode);
    if (data) {
      data.clicks += 1;
      this.store.set(shortCode, data);
      return true;
    }
    return false;
  }
}

// Export a singleton instance
export const urlStore = new UrlStore();
