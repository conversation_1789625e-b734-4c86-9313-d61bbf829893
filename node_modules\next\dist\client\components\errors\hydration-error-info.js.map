{"version": 3, "sources": ["../../../../src/client/components/errors/hydration-error-info.ts"], "sourcesContent": ["import {\n  getHydrationErrorStackInfo,\n  testReactHydrationWarning,\n} from '../is-hydration-error'\n\nexport type HydrationErrorState = {\n  // Hydration warning template format: <message> <serverContent> <clientContent>\n  warning?: [string, string, string]\n  serverContent?: string\n  clientContent?: string\n  // React 19 hydration diff format: <notes> <link> <component diff?>\n  notes?: string\n  reactOutputComponentDiff?: string\n}\n\ntype NullableText = string | null | undefined\n\nexport const hydrationErrorState: HydrationErrorState = {}\n\n// https://github.com/facebook/react/blob/main/packages/react-dom/src/__tests__/ReactDOMHydrationDiff-test.js used as a reference\nconst htmlTagsWarnings = new Set([\n  'Warning: In HTML, %s cannot be a child of <%s>.%s\\nThis will cause a hydration error.%s',\n  'Warning: In HTML, %s cannot be a descendant of <%s>.\\nThis will cause a hydration error.%s',\n  'Warning: In HTML, text nodes cannot be a child of <%s>.\\nThis will cause a hydration error.',\n  \"Warning: In HTML, whitespace text nodes cannot be a child of <%s>. Make sure you don't have any extra whitespace between tags on each line of your source code.\\nThis will cause a hydration error.\",\n  'Warning: Expected server HTML to contain a matching <%s> in <%s>.%s',\n  'Warning: Did not expect server HTML to contain a <%s> in <%s>.%s',\n])\nconst textAndTagsMismatchWarnings = new Set([\n  'Warning: Expected server HTML to contain a matching text node for \"%s\" in <%s>.%s',\n  'Warning: Did not expect server HTML to contain the text node \"%s\" in <%s>.%s',\n])\n\nexport const getHydrationWarningType = (\n  message: NullableText\n): 'tag' | 'text' | 'text-in-tag' => {\n  if (typeof message !== 'string') {\n    // TODO: Doesn't make sense to treat no message as a hydration error message.\n    // We should bail out somewhere earlier.\n    return 'text'\n  }\n\n  const normalizedMessage = message.startsWith('Warning: ')\n    ? message\n    : `Warning: ${message}`\n\n  if (isHtmlTagsWarning(normalizedMessage)) return 'tag'\n  if (isTextInTagsMismatchWarning(normalizedMessage)) return 'text-in-tag'\n\n  return 'text'\n}\n\nconst isHtmlTagsWarning = (message: string) => htmlTagsWarnings.has(message)\n\nconst isTextInTagsMismatchWarning = (msg: string) =>\n  textAndTagsMismatchWarnings.has(msg)\n\nexport const getReactHydrationDiffSegments = (msg: NullableText) => {\n  if (msg) {\n    const { message, diff } = getHydrationErrorStackInfo(msg)\n    if (message) return [message, diff]\n  }\n  return undefined\n}\n\n/**\n * Patch console.error to capture hydration errors.\n * If any of the knownHydrationWarnings are logged, store the message and component stack.\n * When the hydration runtime error is thrown, the message and component stack are added to the error.\n * This results in a more helpful error message in the error overlay.\n */\n\nexport function storeHydrationErrorStateFromConsoleArgs(...args: any[]) {\n  let [msg, firstContent, secondContent, ...rest] = args\n  if (testReactHydrationWarning(msg)) {\n    // Some hydration warnings has 4 arguments, some has 3, fallback to the last argument\n    // when the 3rd argument is not the component stack but an empty string\n    const isReact18 = msg.startsWith('Warning: ')\n\n    // For some warnings, there's only 1 argument for template.\n    // The second argument is the diff or component stack.\n    if (args.length === 3) {\n      secondContent = ''\n    }\n\n    const warning: [string, string, string] = [\n      // remove the last %s from the message\n      msg,\n      firstContent,\n      secondContent,\n    ]\n\n    const lastArg = (rest[rest.length - 1] || '').trim()\n    if (!isReact18) {\n      hydrationErrorState.reactOutputComponentDiff = lastArg\n    } else {\n      hydrationErrorState.reactOutputComponentDiff =\n        generateHydrationDiffReact18(msg, firstContent, secondContent, lastArg)\n    }\n\n    hydrationErrorState.warning = warning\n    hydrationErrorState.serverContent = firstContent\n    hydrationErrorState.clientContent = secondContent\n  }\n}\n\n/*\n * Some hydration errors in React 18 does not have the diff in the error message.\n * Instead it has the error stack trace which is component stack that we can leverage.\n * Will parse the diff from the error stack trace\n *  e.g.\n *  Warning: Expected server HTML to contain a matching <div> in <p>.\n *    at div\n *    at p\n *    at div\n *    at div\n *    at Page\n *  output:\n *    <Page>\n *      <div>\n *        <p>\n *  >       <div>\n *\n */\nfunction generateHydrationDiffReact18(\n  message: string,\n  firstContent: string,\n  secondContent: string,\n  lastArg: string\n) {\n  const componentStack = lastArg\n  let firstIndex = -1\n  let secondIndex = -1\n  const hydrationWarningType = getHydrationWarningType(message)\n\n  // at div\\n at Foo\\n at Bar (....)\\n -> [div, Foo]\n  const components = componentStack\n    .split('\\n')\n    // .reverse()\n    .map((line: string, index: number) => {\n      // `<space>at <component> (<location>)` -> `at <component> (<location>)`\n      line = line.trim()\n      // extract `<space>at <component>` to `<<component>>`\n      // e.g. `  at Foo` -> `<Foo>`\n      const [, component, location] = /at (\\w+)( \\((.*)\\))?/.exec(line) || []\n      // If there's no location then it's user-land stack frame\n      if (!location) {\n        if (component === firstContent && firstIndex === -1) {\n          firstIndex = index\n        } else if (component === secondContent && secondIndex === -1) {\n          secondIndex = index\n        }\n      }\n      return location ? '' : component\n    })\n    .filter(Boolean)\n    .reverse()\n\n  let diff = ''\n  for (let i = 0; i < components.length; i++) {\n    const component = components[i]\n    const matchFirstContent =\n      hydrationWarningType === 'tag' && i === components.length - firstIndex - 1\n    const matchSecondContent =\n      hydrationWarningType === 'tag' &&\n      i === components.length - secondIndex - 1\n    if (matchFirstContent || matchSecondContent) {\n      const spaces = ' '.repeat(Math.max(i * 2 - 2, 0) + 2)\n      diff += `> ${spaces}<${component}>\\n`\n    } else {\n      const spaces = ' '.repeat(i * 2 + 2)\n      diff += `${spaces}<${component}>\\n`\n    }\n  }\n  if (hydrationWarningType === 'text') {\n    const spaces = ' '.repeat(components.length * 2)\n    diff += `+ ${spaces}\"${firstContent}\"\\n`\n    diff += `- ${spaces}\"${secondContent}\"\\n`\n  } else if (hydrationWarningType === 'text-in-tag') {\n    const spaces = ' '.repeat(components.length * 2)\n    diff += `> ${spaces}<${secondContent}>\\n`\n    diff += `>   ${spaces}\"${firstContent}\"\\n`\n  }\n  return diff\n}\n"], "names": ["getHydrationWarningType", "getReactHydrationDiffSegments", "hydrationErrorState", "storeHydrationErrorStateFromConsoleArgs", "htmlTagsWarnings", "Set", "textAndTagsMismatchWarnings", "message", "normalizedMessage", "startsWith", "isHtmlTagsWarning", "isTextInTagsMismatchWarning", "has", "msg", "diff", "getHydrationErrorStackInfo", "undefined", "args", "firstContent", "second<PERSON><PERSON>nt", "rest", "testReactHydrationWarning", "isReact18", "length", "warning", "lastArg", "trim", "reactOutputComponentDiff", "generateHydrationDiffReact18", "serverContent", "clientContent", "componentStack", "firstIndex", "secondIndex", "hydrationWarningType", "components", "split", "map", "line", "index", "component", "location", "exec", "filter", "Boolean", "reverse", "i", "match<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "matchSecondContent", "spaces", "repeat", "Math", "max"], "mappings": ";;;;;;;;;;;;;;;;;IAiCaA,uBAAuB;eAAvBA;;IAwBAC,6BAA6B;eAA7BA;;IAxCAC,mBAAmB;eAAnBA;;IAuDGC,uCAAuC;eAAvCA;;;kCArET;AAcA,MAAMD,sBAA2C,CAAC;AAEzD,iIAAiI;AACjI,MAAME,mBAAmB,IAAIC,IAAI;IAC/B;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,8BAA8B,IAAID,IAAI;IAC1C;IACA;CACD;AAEM,MAAML,0BAA0B,CACrCO;IAEA,IAAI,OAAOA,YAAY,UAAU;QAC/B,6EAA6E;QAC7E,wCAAwC;QACxC,OAAO;IACT;IAEA,MAAMC,oBAAoBD,QAAQE,UAAU,CAAC,eACzCF,UACA,AAAC,cAAWA;IAEhB,IAAIG,kBAAkBF,oBAAoB,OAAO;IACjD,IAAIG,4BAA4BH,oBAAoB,OAAO;IAE3D,OAAO;AACT;AAEA,MAAME,oBAAoB,CAACH,UAAoBH,iBAAiBQ,GAAG,CAACL;AAEpE,MAAMI,8BAA8B,CAACE,MACnCP,4BAA4BM,GAAG,CAACC;AAE3B,MAAMZ,gCAAgC,CAACY;IAC5C,IAAIA,KAAK;QACP,MAAM,EAAEN,OAAO,EAAEO,IAAI,EAAE,GAAGC,IAAAA,4CAA0B,EAACF;QACrD,IAAIN,SAAS,OAAO;YAACA;YAASO;SAAK;IACrC;IACA,OAAOE;AACT;AASO,SAASb;IAAwC,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGc,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAAc;;IACpE,IAAI,CAACJ,KAAKK,cAAcC,eAAe,GAAGC,KAAK,GAAGH;IAClD,IAAII,IAAAA,2CAAyB,EAACR,MAAM;QAClC,qFAAqF;QACrF,uEAAuE;QACvE,MAAMS,YAAYT,IAAIJ,UAAU,CAAC;QAEjC,2DAA2D;QAC3D,sDAAsD;QACtD,IAAIQ,KAAKM,MAAM,KAAK,GAAG;YACrBJ,gBAAgB;QAClB;QAEA,MAAMK,UAAoC;YACxC,sCAAsC;YACtCX;YACAK;YACAC;SACD;QAED,MAAMM,UAAU,AAACL,CAAAA,IAAI,CAACA,KAAKG,MAAM,GAAG,EAAE,IAAI,EAAC,EAAGG,IAAI;QAClD,IAAI,CAACJ,WAAW;YACdpB,oBAAoByB,wBAAwB,GAAGF;QACjD,OAAO;YACLvB,oBAAoByB,wBAAwB,GAC1CC,6BAA6Bf,KAAKK,cAAcC,eAAeM;QACnE;QAEAvB,oBAAoBsB,OAAO,GAAGA;QAC9BtB,oBAAoB2B,aAAa,GAAGX;QACpChB,oBAAoB4B,aAAa,GAAGX;IACtC;AACF;AAEA;;;;;;;;;;;;;;;;;CAiBC,GACD,SAASS,6BACPrB,OAAe,EACfW,YAAoB,EACpBC,aAAqB,EACrBM,OAAe;IAEf,MAAMM,iBAAiBN;IACvB,IAAIO,aAAa,CAAC;IAClB,IAAIC,cAAc,CAAC;IACnB,MAAMC,uBAAuBlC,wBAAwBO;IAErD,kDAAkD;IAClD,MAAM4B,aAAaJ,eAChBK,KAAK,CAAC,KACP,aAAa;KACZC,GAAG,CAAC,CAACC,MAAcC;QAClB,wEAAwE;QACxED,OAAOA,KAAKZ,IAAI;QAChB,qDAAqD;QACrD,6BAA6B;QAC7B,MAAM,GAAGc,WAAWC,SAAS,GAAG,uBAAuBC,IAAI,CAACJ,SAAS,EAAE;QACvE,yDAAyD;QACzD,IAAI,CAACG,UAAU;YACb,IAAID,cAActB,gBAAgBc,eAAe,CAAC,GAAG;gBACnDA,aAAaO;YACf,OAAO,IAAIC,cAAcrB,iBAAiBc,gBAAgB,CAAC,GAAG;gBAC5DA,cAAcM;YAChB;QACF;QACA,OAAOE,WAAW,KAAKD;IACzB,GACCG,MAAM,CAACC,SACPC,OAAO;IAEV,IAAI/B,OAAO;IACX,IAAK,IAAIgC,IAAI,GAAGA,IAAIX,WAAWZ,MAAM,EAAEuB,IAAK;QAC1C,MAAMN,YAAYL,UAAU,CAACW,EAAE;QAC/B,MAAMC,oBACJb,yBAAyB,SAASY,MAAMX,WAAWZ,MAAM,GAAGS,aAAa;QAC3E,MAAMgB,qBACJd,yBAAyB,SACzBY,MAAMX,WAAWZ,MAAM,GAAGU,cAAc;QAC1C,IAAIc,qBAAqBC,oBAAoB;YAC3C,MAAMC,SAAS,IAAIC,MAAM,CAACC,KAAKC,GAAG,CAACN,IAAI,IAAI,GAAG,KAAK;YACnDhC,QAAQ,AAAC,OAAImC,SAAO,MAAGT,YAAU;QACnC,OAAO;YACL,MAAMS,SAAS,IAAIC,MAAM,CAACJ,IAAI,IAAI;YAClChC,QAAQ,AAAGmC,SAAO,MAAGT,YAAU;QACjC;IACF;IACA,IAAIN,yBAAyB,QAAQ;QACnC,MAAMe,SAAS,IAAIC,MAAM,CAACf,WAAWZ,MAAM,GAAG;QAC9CT,QAAQ,AAAC,OAAImC,SAAO,MAAG/B,eAAa;QACpCJ,QAAQ,AAAC,OAAImC,SAAO,MAAG9B,gBAAc;IACvC,OAAO,IAAIe,yBAAyB,eAAe;QACjD,MAAMe,SAAS,IAAIC,MAAM,CAACf,WAAWZ,MAAM,GAAG;QAC9CT,QAAQ,AAAC,OAAImC,SAAO,MAAG9B,gBAAc;QACrCL,QAAQ,AAAC,SAAMmC,SAAO,MAAG/B,eAAa;IACxC;IACA,OAAOJ;AACT"}