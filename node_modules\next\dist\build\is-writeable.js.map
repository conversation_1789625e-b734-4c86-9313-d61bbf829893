{"version": 3, "sources": ["../../src/build/is-writeable.ts"], "sourcesContent": ["import fs from 'fs'\n\nexport async function isWriteable(directory: string): Promise<boolean> {\n  try {\n    await fs.promises.access(directory, (fs.constants || fs).W_OK)\n    return true\n  } catch (err) {\n    return false\n  }\n}\n"], "names": ["isWriteable", "directory", "fs", "promises", "access", "constants", "W_OK", "err"], "mappings": ";;;;+BAEsBA;;;eAAAA;;;2DAFP;;;;;;AAER,eAAeA,YAAYC,SAAiB;IACjD,IAAI;QACF,MAAMC,WAAE,CAACC,QAAQ,CAACC,MAAM,CAACH,WAAW,AAACC,CAAAA,WAAE,CAACG,SAAS,IAAIH,WAAE,AAAD,EAAGI,IAAI;QAC7D,OAAO;IACT,EAAE,OAAOC,KAAK;QACZ,OAAO;IACT;AACF"}