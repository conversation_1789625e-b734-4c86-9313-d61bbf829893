import { NextRequest, NextResponse } from 'next/server';
import { urlStore } from '@/lib/urlStore';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ shortCode: string }> }
) {
  const { shortCode } = await params;

  const linkData = urlStore.get(shortCode);

  if (!linkData) {
    return NextResponse.json({ error: 'Link not found' }, { status: 404 });
  }

  return NextResponse.json({
    originalUrl: linkData.originalUrl,
    clicks: linkData.clicks,
    createdAt: linkData.createdAt.toISOString()
  });
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ shortCode: string }> }
) {
  const { shortCode } = await params;

  const success = urlStore.incrementClicks(shortCode);

  if (!success) {
    return NextResponse.json({ error: 'Link not found' }, { status: 404 });
  }

  const linkData = urlStore.get(shortCode);

  return NextResponse.json({
    success: true,
    clicks: linkData?.clicks || 0
  });
}
