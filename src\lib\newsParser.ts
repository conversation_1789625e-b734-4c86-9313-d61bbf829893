export interface NewsArticle {
  id: string;
  title: string;
  description: string;
  category: string;
  timeAgo: string;
  imageUrl?: string;
  articleUrl?: string;
  emoji: string;
  gradient: string;
}

// Get news emoji based on category
function getNewsEmoji(category: string): string {
  const categoryLower = category.toLowerCase();
  
  if (categoryLower.includes('release') || categoryLower.includes('launch')) return '🎮';
  if (categoryLower.includes('esports') || categoryLower.includes('tournament')) return '🏆';
  if (categoryLower.includes('update') || categoryLower.includes('patch')) return '🔥';
  if (categoryLower.includes('review') || categoryLower.includes('rating')) return '⭐';
  if (categoryLower.includes('news') || categoryLower.includes('announcement')) return '📰';
  if (categoryLower.includes('trailer') || categoryLower.includes('video')) return '🎬';
  if (categoryLower.includes('beta') || categoryLower.includes('alpha')) return '🧪';
  if (categoryLower.includes('dlc') || categoryLower.includes('expansion')) return '📦';
  if (categoryLower.includes('sale') || categoryLower.includes('discount')) return '💰';
  if (categoryLower.includes('hardware') || categoryLower.includes('console')) return '🖥️';
  
  return '🎮'; // Default gaming emoji
}

// Get gradient based on category
function getNewsGradient(category: string): string {
  const categoryLower = category.toLowerCase();
  
  if (categoryLower.includes('release') || categoryLower.includes('launch')) return 'from-blue-500 to-purple-600';
  if (categoryLower.includes('esports') || categoryLower.includes('tournament')) return 'from-green-500 to-blue-600';
  if (categoryLower.includes('update') || categoryLower.includes('patch')) return 'from-red-500 to-pink-600';
  if (categoryLower.includes('review') || categoryLower.includes('rating')) return 'from-yellow-500 to-orange-600';
  if (categoryLower.includes('news') || categoryLower.includes('announcement')) return 'from-purple-500 to-pink-600';
  if (categoryLower.includes('trailer') || categoryLower.includes('video')) return 'from-cyan-500 to-blue-600';
  if (categoryLower.includes('beta') || categoryLower.includes('alpha')) return 'from-orange-500 to-red-600';
  if (categoryLower.includes('dlc') || categoryLower.includes('expansion')) return 'from-teal-500 to-green-600';
  if (categoryLower.includes('sale') || categoryLower.includes('discount')) return 'from-green-600 to-emerald-600';
  if (categoryLower.includes('hardware') || categoryLower.includes('console')) return 'from-gray-500 to-slate-600';
  
  return 'from-blue-500 to-purple-600'; // Default gradient
}

// Parse news string format: TITLE|DESCRIPTION|CATEGORY|TIME_AGO|ARTICLE_URL(optional)
function parseNewsString(newsString: string, newsId: string): NewsArticle | null {
  try {
    const parts = newsString.split('|');
    if (parts.length < 4) {
      console.warn(`Invalid news format for ${newsId}: ${newsString}`);
      return null;
    }

    const [title, description, category, timeAgo, articleUrl] = parts;

    return {
      id: newsId,
      title: title.trim(),
      description: description.trim(),
      category: category.trim(),
      timeAgo: timeAgo.trim(),
      articleUrl: articleUrl?.trim() || undefined,
      emoji: getNewsEmoji(category),
      gradient: getNewsGradient(category)
    };
  } catch (error) {
    console.error(`Error parsing news ${newsId}:`, error);
    return null;
  }
}

// Get all news from environment variables
export function getNewsFromEnv(): NewsArticle[] {
  const news: NewsArticle[] = [];
  
  // Look for environment variables starting with NEWS_
  const envVars = process.env;
  const newsKeys = Object.keys(envVars)
    .filter(key => key.startsWith('NEWS_'))
    .sort((a, b) => {
      // Sort by number: NEWS_1, NEWS_2, etc.
      const numA = parseInt(a.replace('NEWS_', ''));
      const numB = parseInt(b.replace('NEWS_', ''));
      return numA - numB;
    });

  for (const key of newsKeys) {
    const newsString = envVars[key];
    if (!newsString) continue;

    const parsedNews = parseNewsString(newsString, key);
    if (parsedNews) {
      news.push(parsedNews);
    }
  }

  return news;
}

// No default news - site will only show content from environment variables
