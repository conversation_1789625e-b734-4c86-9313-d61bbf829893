{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/dialog/dialog-content.tsx"], "sourcesContent": ["import * as React from 'react'\n\nexport type DialogContentProps = {\n  children?: React.ReactNode\n  className?: string\n}\n\nconst DialogContent: React.FC<DialogContentProps> = function DialogContent({\n  children,\n  className,\n}) {\n  return (\n    <div data-nextjs-dialog-content className={className}>\n      {children}\n    </div>\n  )\n}\n\nexport { DialogContent }\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "className", "div", "data-nextjs-dialog-content"], "mappings": ";;;;+BAkBSA;;;eAAAA;;;;;iEAlBc;AAOvB,MAAMA,gBAA8C,SAASA,cAAc,KAG1E;IAH0E,IAAA,EACzEC,QAAQ,EACRC,SAAS,EACV,GAH0E;IAIzE,qBACE,qBAACC;QAAIC,4BAA0B;QAACF,WAAWA;kBACxCD;;AAGP"}