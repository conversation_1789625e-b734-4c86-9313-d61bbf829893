'use client';

import Link from "next/link";
import { useState, useEffect } from "react";
import DealsSection from "@/components/DealsSection";
import NewsSection from "@/components/NewsSection";

interface ShortenedLink {
  shortCode: string;
  originalUrl: string;
  shortUrl: string;
  clicks: number;
  createdAt: string;
}

export default function Home() {
  const [url, setUrl] = useState('');
  const [shortenedUrl, setShortenedUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [recentLinks, setRecentLinks] = useState<ShortenedLink[]>([]);
  const [copied, setCopied] = useState(false);

  // Fetch recent links on component mount
  useEffect(() => {
    fetchRecentLinks();
  }, []);

  const fetchRecentLinks = async () => {
    try {
      const response = await fetch('/api/shorten');
      if (response.ok) {
        const data = await response.json();
        setRecentLinks(data.links);
      }
    } catch (error) {
      console.error('Error fetching recent links:', error);
    }
  };

  const handleShorten = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!url.trim()) return;

    setIsLoading(true);
    setError('');
    setShortenedUrl('');

    try {
      const response = await fetch('/api/shorten', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: url.trim() }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to shorten URL');
      }

      setShortenedUrl(data.shortUrl);
      setUrl('');
      // Refresh recent links
      fetchRecentLinks();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shortenedUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Navigation */}
      <nav className="bg-black/20 backdrop-blur-sm border-b border-purple-500/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-white">
                🎮 <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">GameHub</span>
              </h1>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <Link href="#news" className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">
                  News
                </Link>
                <Link href="#deals" className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">
                  Deals
                </Link>
                <Link href="#shortener" className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">
                  Link Shortener
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Your Ultimate <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Gaming Hub</span>
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Stay updated with the latest gaming news, discover amazing deals on gaming products, and shorten your links with our powerful tools.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105">
              Explore News
            </button>
            <button className="border border-purple-500 text-purple-300 px-8 py-3 rounded-lg font-semibold hover:bg-purple-500/10 transition-all">
              View Deals
            </button>
          </div>
        </div>
      </section>

      {/* Gaming News Section - Dynamic from Environment Variables */}
      <NewsSection id="news" />

      {/* Gaming Deals Section - Dynamic from Environment Variables */}
      <DealsSection id="deals" />

      {/* Link Shortener Section */}
      <section id="shortener" className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h3 className="text-3xl font-bold text-white mb-8">Link Shortener</h3>
          <p className="text-gray-300 mb-8">Shorten your gaming links and track their performance</p>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8">
            <form onSubmit={handleShorten} className="mb-6">
              <div className="flex flex-col sm:flex-row gap-4 mb-4">
                <input
                  type="url"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  placeholder="Enter your long URL here..."
                  className="flex-1 px-4 py-3 rounded-lg bg-white/20 border border-purple-500/30 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                  required
                />
                <button
                  type="submit"
                  disabled={isLoading}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Shortening...' : 'Shorten'}
                </button>
              </div>

              {error && (
                <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3 mb-4">
                  <p className="text-red-300 text-sm">{error}</p>
                </div>
              )}

              {shortenedUrl && (
                <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4 mb-4">
                  <p className="text-green-300 text-sm mb-2">Your shortened URL:</p>
                  <div className="flex items-center gap-2">
                    <input
                      type="text"
                      value={shortenedUrl}
                      readOnly
                      className="flex-1 px-3 py-2 bg-white/10 border border-green-500/30 rounded text-white font-mono text-sm"
                    />
                    <button
                      type="button"
                      onClick={copyToClipboard}
                      className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm transition-colors"
                    >
                      {copied ? 'Copied!' : 'Copy'}
                    </button>
                  </div>
                </div>
              )}
            </form>

            <div className="text-left">
              <h4 className="text-white font-semibold mb-4">Recent Links</h4>
              <div className="space-y-3">
                {recentLinks.length > 0 ? (
                  recentLinks.map((link) => (
                    <div key={link.shortCode} className="flex items-center justify-between bg-white/5 rounded-lg p-4">
                      <div className="flex-1 min-w-0">
                        <p className="text-purple-400 font-mono">{link.shortUrl.replace('http://localhost:3000', 'gamehub.ly')}</p>
                        <p className="text-gray-400 text-sm truncate">{link.originalUrl}</p>
                      </div>
                      <div className="text-right ml-4">
                        <p className="text-white font-semibold">{link.clicks.toLocaleString()} clicks</p>
                        <p className="text-gray-400 text-sm">
                          {new Date(link.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <p className="text-gray-400">No links shortened yet. Create your first short link above!</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black/40 border-t border-purple-500/20 py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <p className="text-gray-400">© 2024 GameHub. Built with Next.js and Tailwind CSS.</p>
        </div>
      </footer>
    </div>
  );
}
