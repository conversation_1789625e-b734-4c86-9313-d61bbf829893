{"version": 3, "sources": ["../../../../../src/build/webpack/config/blocks/base.ts"], "sourcesContent": ["import curry from 'next/dist/compiled/lodash.curry'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { COMPILER_NAMES } from '../../../../shared/lib/constants'\nimport type { ConfigurationContext } from '../utils'\nimport DevToolsIgnorePlugin from '../../plugins/devtools-ignore-list-plugin'\nimport EvalSourceMapDevToolPlugin from '../../plugins/eval-source-map-dev-tool-plugin'\n\nfunction shouldIgnorePath(modulePath: string): boolean {\n  return (\n    modulePath.includes('node_modules') ||\n    // Only relevant for when Next.js is symlinked e.g. in the Next.js monorepo\n    modulePath.includes('next/dist')\n  )\n}\n\nexport const base = curry(function base(\n  ctx: ConfigurationContext,\n  config: webpack.Configuration\n) {\n  config.mode = ctx.isDevelopment ? 'development' : 'production'\n  config.name = ctx.isServer\n    ? ctx.isEdgeRuntime\n      ? COMPILER_NAMES.edgeServer\n      : COMPILER_NAMES.server\n    : COMPILER_NAMES.client\n\n  config.target = !ctx.targetWeb\n    ? 'node18.17' // Same version defined in packages/next/package.json#engines\n    : ctx.isEdgeRuntime\n      ? ['web', 'es6']\n      : ['web', 'es6']\n\n  // https://webpack.js.org/configuration/devtool/#development\n  if (ctx.isDevelopment) {\n    if (process.env.__NEXT_TEST_MODE && !process.env.__NEXT_TEST_WITH_DEVTOOL) {\n      config.devtool = false\n    } else {\n      // `eval-source-map` provides full-fidelity source maps for the\n      // original source, including columns and original variable names.\n      // This is desirable so the in-browser debugger can correctly pause\n      // and show scoped variables with their original names.\n      config.devtool = 'eval-source-map'\n    }\n  } else {\n    if (\n      ctx.isEdgeRuntime ||\n      (ctx.isServer && ctx.serverSourceMaps) ||\n      // Enable browser sourcemaps:\n      (ctx.productionBrowserSourceMaps && ctx.isClient)\n    ) {\n      config.devtool = 'source-map'\n    } else {\n      config.devtool = false\n    }\n  }\n\n  if (!config.module) {\n    config.module = { rules: [] }\n  }\n\n  config.plugins ??= []\n  if (config.devtool === 'source-map' && !process.env.NEXT_RSPACK) {\n    config.plugins.push(\n      new DevToolsIgnorePlugin({\n        shouldIgnorePath,\n      })\n    )\n  } else if (config.devtool === 'eval-source-map' && !process.env.NEXT_RSPACK) {\n    // We're using a fork of `eval-source-map`\n    config.devtool = false\n    config.plugins.push(\n      new EvalSourceMapDevToolPlugin({\n        moduleFilenameTemplate: config.output?.devtoolModuleFilenameTemplate,\n        shouldIgnorePath,\n      })\n    )\n  }\n\n  // TODO: add codemod for \"Should not import the named export\" with JSON files\n  // config.module.strictExportPresence = !isWebpack5\n\n  return config\n})\n"], "names": ["base", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modulePath", "includes", "curry", "ctx", "config", "mode", "isDevelopment", "name", "isServer", "isEdgeRuntime", "COMPILER_NAMES", "edgeServer", "server", "client", "target", "targetWeb", "process", "env", "__NEXT_TEST_MODE", "__NEXT_TEST_WITH_DEVTOOL", "devtool", "serverSourceMaps", "productionBrowserSourceMaps", "isClient", "module", "rules", "plugins", "NEXT_RSPACK", "push", "DevToolsIgnorePlugin", "EvalSourceMapDevToolPlugin", "moduleFilenameTemplate", "output", "devtoolModuleFilenameTemplate"], "mappings": ";;;;+BAeaA;;;eAAAA;;;oEAfK;2BAEa;iFAEE;mFACM;;;;;;AAEvC,SAASC,iBAAiBC,UAAkB;IAC1C,OACEA,WAAWC,QAAQ,CAAC,mBACpB,2EAA2E;IAC3ED,WAAWC,QAAQ,CAAC;AAExB;AAEO,MAAMH,OAAOI,IAAAA,oBAAK,EAAC,SAASJ,KACjCK,GAAyB,EACzBC,MAA6B;IAE7BA,OAAOC,IAAI,GAAGF,IAAIG,aAAa,GAAG,gBAAgB;IAClDF,OAAOG,IAAI,GAAGJ,IAAIK,QAAQ,GACtBL,IAAIM,aAAa,GACfC,yBAAc,CAACC,UAAU,GACzBD,yBAAc,CAACE,MAAM,GACvBF,yBAAc,CAACG,MAAM;IAEzBT,OAAOU,MAAM,GAAG,CAACX,IAAIY,SAAS,GAC1B,YAAY,6DAA6D;OACzEZ,IAAIM,aAAa,GACf;QAAC;QAAO;KAAM,GACd;QAAC;QAAO;KAAM;IAEpB,4DAA4D;IAC5D,IAAIN,IAAIG,aAAa,EAAE;QACrB,IAAIU,QAAQC,GAAG,CAACC,gBAAgB,IAAI,CAACF,QAAQC,GAAG,CAACE,wBAAwB,EAAE;YACzEf,OAAOgB,OAAO,GAAG;QACnB,OAAO;YACL,+DAA+D;YAC/D,kEAAkE;YAClE,mEAAmE;YACnE,uDAAuD;YACvDhB,OAAOgB,OAAO,GAAG;QACnB;IACF,OAAO;QACL,IACEjB,IAAIM,aAAa,IAChBN,IAAIK,QAAQ,IAAIL,IAAIkB,gBAAgB,IACrC,6BAA6B;QAC5BlB,IAAImB,2BAA2B,IAAInB,IAAIoB,QAAQ,EAChD;YACAnB,OAAOgB,OAAO,GAAG;QACnB,OAAO;YACLhB,OAAOgB,OAAO,GAAG;QACnB;IACF;IAEA,IAAI,CAAChB,OAAOoB,MAAM,EAAE;QAClBpB,OAAOoB,MAAM,GAAG;YAAEC,OAAO,EAAE;QAAC;IAC9B;IAEArB,OAAOsB,OAAO,KAAK,EAAE;IACrB,IAAItB,OAAOgB,OAAO,KAAK,gBAAgB,CAACJ,QAAQC,GAAG,CAACU,WAAW,EAAE;QAC/DvB,OAAOsB,OAAO,CAACE,IAAI,CACjB,IAAIC,iCAAoB,CAAC;YACvB9B;QACF;IAEJ,OAAO,IAAIK,OAAOgB,OAAO,KAAK,qBAAqB,CAACJ,QAAQC,GAAG,CAACU,WAAW,EAAE;YAK/CvB;QAJ5B,0CAA0C;QAC1CA,OAAOgB,OAAO,GAAG;QACjBhB,OAAOsB,OAAO,CAACE,IAAI,CACjB,IAAIE,mCAA0B,CAAC;YAC7BC,sBAAsB,GAAE3B,iBAAAA,OAAO4B,MAAM,qBAAb5B,eAAe6B,6BAA6B;YACpElC;QACF;IAEJ;IAEA,6EAA6E;IAC7E,mDAAmD;IAEnD,OAAOK;AACT"}