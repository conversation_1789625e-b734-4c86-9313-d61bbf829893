{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/next-types-plugin/index.ts"], "sourcesContent": ["import type { Rewrite, Redirect } from '../../../../lib/load-custom-routes'\nimport type { Token } from 'next/dist/compiled/path-to-regexp'\n\nimport fs from 'fs/promises'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport { parse } from 'next/dist/compiled/path-to-regexp'\nimport path from 'path'\n\nimport { WEBPACK_LAYERS } from '../../../../lib/constants'\nimport { denormalizePagePath } from '../../../../shared/lib/page-path/denormalize-page-path'\nimport { ensureLeadingSlash } from '../../../../shared/lib/page-path/ensure-leading-slash'\nimport { normalizePathSep } from '../../../../shared/lib/page-path/normalize-path-sep'\nimport { HTTP_METHODS } from '../../../../server/web/http'\nimport { isDynamicRoute } from '../../../../shared/lib/router/utils'\nimport { normalizeAppPath } from '../../../../shared/lib/router/utils/app-paths'\nimport { getPageFromPath } from '../../../entries'\nimport type { PageExtensions } from '../../../page-extensions-type'\nimport { devPageFiles } from './shared'\nimport { getProxiedPluginState } from '../../../build-context'\nimport type { CacheLife } from '../../../../server/use-cache/cache-life'\n\nconst PLUGIN_NAME = 'NextTypesPlugin'\n\ntype Rewrites = {\n  fallback: Rewrite[]\n  afterFiles: Rewrite[]\n  beforeFiles: Rewrite[]\n}\n\ninterface Options {\n  dir: string\n  distDir: string\n  appDir: string\n  dev: boolean\n  isEdgeServer: boolean\n  pageExtensions: PageExtensions\n  typedRoutes: boolean\n  cacheLifeConfig: undefined | { [profile: string]: CacheLife }\n  originalRewrites: Rewrites | undefined\n  originalRedirects: Redirect[] | undefined\n}\n\nfunction createTypeGuardFile(\n  fullPath: string,\n  relativePath: string,\n  options: {\n    type: 'layout' | 'page' | 'route'\n    slots?: string[]\n  }\n) {\n  return `// File: ${fullPath}\nimport * as entry from '${relativePath}.js'\n${\n  options.type === 'route'\n    ? `import type { NextRequest } from 'next/server.js'`\n    : `import type { ResolvingMetadata, ResolvingViewport } from 'next/dist/lib/metadata/types/metadata-interface.js'`\n}\n\ntype TEntry = typeof import('${relativePath}.js')\n\ntype SegmentParams<T extends Object = any> = T extends Record<string, any>\n  ? { [K in keyof T]: T[K] extends string ? string | string[] | undefined : never }\n  : T\n\n// Check that the entry is a valid entry\ncheckFields<Diff<{\n  ${\n    options.type === 'route'\n      ? HTTP_METHODS.map((method) => `${method}?: Function`).join('\\n  ')\n      : 'default: Function'\n  }\n  config?: {}\n  generateStaticParams?: Function\n  revalidate?: RevalidateRange<TEntry> | false\n  dynamic?: 'auto' | 'force-dynamic' | 'error' | 'force-static'\n  dynamicParams?: boolean\n  fetchCache?: 'auto' | 'force-no-store' | 'only-no-store' | 'default-no-store' | 'default-cache' | 'only-cache' | 'force-cache'\n  preferredRegion?: 'auto' | 'global' | 'home' | string | string[]\n  runtime?: 'nodejs' | 'experimental-edge' | 'edge'\n  maxDuration?: number\n  ${\n    options.type === 'route'\n      ? ''\n      : `\n  metadata?: any\n  generateMetadata?: Function\n  viewport?: any\n  generateViewport?: Function\n  experimental_ppr?: boolean\n  `\n  }\n}, TEntry, ''>>()\n\n${options.type === 'route' ? `type RouteContext = { params: Promise<SegmentParams> }` : ''}\n${\n  options.type === 'route'\n    ? HTTP_METHODS.map(\n        (method) => `// Check the prop type of the entry function\nif ('${method}' in entry) {\n  checkFields<\n    Diff<\n      ParamCheck<Request | NextRequest>,\n      {\n        __tag__: '${method}'\n        __param_position__: 'first'\n        __param_type__: FirstArg<MaybeField<TEntry, '${method}'>>\n      },\n      '${method}'\n    >\n  >()\n  checkFields<\n    Diff<\n      ParamCheck<RouteContext>,\n      {\n        __tag__: '${method}'\n        __param_position__: 'second'\n        __param_type__: SecondArg<MaybeField<TEntry, '${method}'>>\n      },\n      '${method}'\n    >\n  >()\n  ${\n    ''\n    // Adding void to support never return type without explicit return:\n    // e.g. notFound() will interrupt the execution but the handler return type is inferred as void.\n    // x-ref: https://github.com/microsoft/TypeScript/issues/16608#issuecomment-309327984\n  }\n  checkFields<\n    Diff<\n      {\n        __tag__: '${method}',\n        __return_type__: Response | void | never | Promise<Response | void | never>\n      },\n      {\n        __tag__: '${method}',\n        __return_type__: ReturnType<MaybeField<TEntry, '${method}'>>\n      },\n      '${method}'\n    >\n  >()\n}\n`\n      ).join('')\n    : `// Check the prop type of the entry function\ncheckFields<Diff<${\n        options.type === 'page' ? 'PageProps' : 'LayoutProps'\n      }, FirstArg<TEntry['default']>, 'default'>>()\n\n// Check the arguments and return type of the generateMetadata function\nif ('generateMetadata' in entry) {\n  checkFields<Diff<${\n    options.type === 'page' ? 'PageProps' : 'LayoutProps'\n  }, FirstArg<MaybeField<TEntry, 'generateMetadata'>>, 'generateMetadata'>>()\n  checkFields<Diff<ResolvingMetadata, SecondArg<MaybeField<TEntry, 'generateMetadata'>>, 'generateMetadata'>>()\n}\n\n// Check the arguments and return type of the generateViewport function\nif ('generateViewport' in entry) {\n  checkFields<Diff<${\n    options.type === 'page' ? 'PageProps' : 'LayoutProps'\n  }, FirstArg<MaybeField<TEntry, 'generateViewport'>>, 'generateViewport'>>()\n  checkFields<Diff<ResolvingViewport, SecondArg<MaybeField<TEntry, 'generateViewport'>>, 'generateViewport'>>()\n}\n`\n}\n// Check the arguments and return type of the generateStaticParams function\nif ('generateStaticParams' in entry) {\n  checkFields<Diff<{ params: SegmentParams }, FirstArg<MaybeField<TEntry, 'generateStaticParams'>>, 'generateStaticParams'>>()\n  checkFields<Diff<{ __tag__: 'generateStaticParams', __return_type__: any[] | Promise<any[]> }, { __tag__: 'generateStaticParams', __return_type__: ReturnType<MaybeField<TEntry, 'generateStaticParams'>> }>>()\n}\n\nexport interface PageProps {\n  params?: Promise<SegmentParams>\n  searchParams?: Promise<any>\n}\nexport interface LayoutProps {\n  children?: React.ReactNode\n${\n  options.slots\n    ? options.slots.map((slot) => `  ${slot}: React.ReactNode`).join('\\n')\n    : ''\n}\n  params?: Promise<SegmentParams>\n}\n\n// =============\n// Utility types\ntype RevalidateRange<T> = T extends { revalidate: any } ? NonNegative<T['revalidate']> : never\n\n// If T is unknown or any, it will be an empty {} type. Otherwise, it will be the same as Omit<T, keyof Base>.\ntype OmitWithTag<T, K extends keyof any, _M> = Omit<T, K>\ntype Diff<Base, T extends Base, Message extends string = ''> = 0 extends (1 & T) ? {} : OmitWithTag<T, keyof Base, Message>\n\ntype FirstArg<T extends Function> = T extends (...args: [infer T, any]) => any ? unknown extends T ? any : T : never\ntype SecondArg<T extends Function> = T extends (...args: [any, infer T]) => any ? unknown extends T ? any : T : never\ntype MaybeField<T, K extends string> = T extends { [k in K]: infer G } ? G extends Function ? G : never : never\n\n${\n  options.type === 'route'\n    ? `type ParamCheck<T> = {\n  __tag__: string\n  __param_position__: string\n  __param_type__: T\n}`\n    : ''\n}\n\nfunction checkFields<_ extends { [k in keyof any]: never }>() {}\n\n// https://github.com/sindresorhus/type-fest\ntype Numeric = number | bigint\ntype Zero = 0 | 0n\ntype Negative<T extends Numeric> = T extends Zero ? never : \\`\\${T}\\` extends \\`-\\${string}\\` ? T : never\ntype NonNegative<T extends Numeric> = T extends Zero ? T : Negative<T> extends never ? T : '__invalid_negative_number__'\n`\n}\n\nasync function collectNamedSlots(layoutPath: string) {\n  const layoutDir = path.dirname(layoutPath)\n  const items = await fs.readdir(layoutDir, { withFileTypes: true })\n  const slots = []\n  for (const item of items) {\n    if (\n      item.isDirectory() &&\n      item.name.startsWith('@') &&\n      // `@children slots are matched to the children prop, and should not be handled separately for type-checking\n      item.name !== '@children'\n    ) {\n      slots.push(item.name.slice(1))\n    }\n  }\n  return slots\n}\n\n// By exposing the static route types separately as string literals,\n// editors can provide autocompletion for them. However it's currently not\n// possible to provide the same experience for dynamic routes.\n\nconst pluginState = getProxiedPluginState({\n  collectedRootParams: {} as Record<string, string[]>,\n  routeTypes: {\n    edge: {\n      static: '',\n      dynamic: '',\n    },\n    node: {\n      static: '',\n      dynamic: '',\n    },\n    extra: {\n      static: '',\n      dynamic: '',\n    },\n  } as Record<'edge' | 'node' | 'extra', Record<'static' | 'dynamic', string>>,\n})\n\nfunction formatRouteToRouteType(route: string) {\n  const isDynamic = isDynamicRoute(route)\n  if (isDynamic) {\n    route = route\n      .split('/')\n      .map((part) => {\n        if (part.startsWith('[') && part.endsWith(']')) {\n          if (part.startsWith('[...')) {\n            // /[...slug]\n            return `\\${CatchAllSlug<T>}`\n          } else if (part.startsWith('[[...') && part.endsWith(']]')) {\n            // /[[...slug]]\n            return `\\${OptionalCatchAllSlug<T>}`\n          }\n          // /[slug]\n          return `\\${SafeSlug<T>}`\n        }\n        return part\n      })\n      .join('/')\n  }\n\n  return {\n    isDynamic,\n    routeType: `\\n    | \\`${route}\\``,\n  }\n}\n\n// Whether redirects and rewrites have been converted into routeTypes or not.\nlet redirectsRewritesTypesProcessed = false\n\n// Convert redirects and rewrites into routeTypes.\nfunction addRedirectsRewritesRouteTypes(\n  rewrites: Rewrites | undefined,\n  redirects: Redirect[] | undefined\n) {\n  function addExtraRoute(source: string) {\n    let tokens: Token[] | undefined\n    try {\n      tokens = parse(source)\n    } catch {\n      // Ignore invalid routes - they will be handled by other checks.\n    }\n\n    if (Array.isArray(tokens)) {\n      const possibleNormalizedRoutes = ['']\n      let slugCnt = 1\n\n      function append(suffix: string) {\n        for (let i = 0; i < possibleNormalizedRoutes.length; i++) {\n          possibleNormalizedRoutes[i] += suffix\n        }\n      }\n\n      function fork(suffix: string) {\n        const currentLength = possibleNormalizedRoutes.length\n        for (let i = 0; i < currentLength; i++) {\n          possibleNormalizedRoutes.push(possibleNormalizedRoutes[i] + suffix)\n        }\n      }\n\n      for (const token of tokens) {\n        if (typeof token === 'object') {\n          // Make sure the slug is always named.\n          const slug =\n            token.name || (slugCnt++ === 1 ? 'slug' : `slug${slugCnt}`)\n\n          if (token.modifier === '*') {\n            append(`${token.prefix}[[...${slug}]]`)\n          } else if (token.modifier === '+') {\n            append(`${token.prefix}[...${slug}]`)\n          } else if (token.modifier === '') {\n            if (token.pattern === '[^\\\\/#\\\\?]+?') {\n              // A safe slug\n              append(`${token.prefix}[${slug}]`)\n            } else if (token.pattern === '.*') {\n              // An optional catch-all slug\n              append(`${token.prefix}[[...${slug}]]`)\n            } else if (token.pattern === '.+') {\n              // A catch-all slug\n              append(`${token.prefix}[...${slug}]`)\n            } else {\n              // Other regex patterns are not supported. Skip this route.\n              return\n            }\n          } else if (token.modifier === '?') {\n            if (/^[a-zA-Z0-9_/]*$/.test(token.pattern)) {\n              // An optional slug with plain text only, fork the route.\n              append(token.prefix)\n              fork(token.pattern)\n            } else {\n              // Optional modifier `?` and regex patterns are not supported.\n              return\n            }\n          }\n        } else if (typeof token === 'string') {\n          append(token)\n        }\n      }\n\n      for (const normalizedRoute of possibleNormalizedRoutes) {\n        const { isDynamic, routeType } = formatRouteToRouteType(normalizedRoute)\n        pluginState.routeTypes.extra[isDynamic ? 'dynamic' : 'static'] +=\n          routeType\n      }\n    }\n  }\n\n  if (rewrites) {\n    for (const rewrite of rewrites.beforeFiles) {\n      addExtraRoute(rewrite.source)\n    }\n    for (const rewrite of rewrites.afterFiles) {\n      addExtraRoute(rewrite.source)\n    }\n    for (const rewrite of rewrites.fallback) {\n      addExtraRoute(rewrite.source)\n    }\n  }\n\n  if (redirects) {\n    for (const redirect of redirects) {\n      // Skip internal redirects\n      // https://github.com/vercel/next.js/blob/8ff3d7ff57836c24088474175d595b4d50b3f857/packages/next/src/lib/load-custom-routes.ts#L704-L710\n      if (!('internal' in redirect)) {\n        addExtraRoute(redirect.source)\n      }\n    }\n  }\n}\n\nfunction createRouteDefinitions() {\n  let staticRouteTypes = ''\n  let dynamicRouteTypes = ''\n\n  for (const type of ['edge', 'node', 'extra'] as const) {\n    staticRouteTypes += pluginState.routeTypes[type].static\n    dynamicRouteTypes += pluginState.routeTypes[type].dynamic\n  }\n\n  // If both StaticRoutes and DynamicRoutes are empty, fallback to type 'string & {}'.\n  const routeTypesFallback =\n    !staticRouteTypes && !dynamicRouteTypes ? 'string & {}' : ''\n\n  return `// Type definitions for Next.js routes\n\n/**\n * Internal types used by the Next.js router and Link component.\n * These types are not meant to be used directly.\n * @internal\n */\ndeclare namespace __next_route_internal_types__ {\n  type SearchOrHash = \\`?\\${string}\\` | \\`#\\${string}\\`\n  type WithProtocol = \\`\\${string}:\\${string}\\`\n\n  type Suffix = '' | SearchOrHash\n\n  type SafeSlug<S extends string> = S extends \\`\\${string}/\\${string}\\`\n    ? never\n    : S extends \\`\\${string}\\${SearchOrHash}\\`\n    ? never\n    : S extends ''\n    ? never\n    : S\n\n  type CatchAllSlug<S extends string> = S extends \\`\\${string}\\${SearchOrHash}\\`\n    ? never\n    : S extends ''\n    ? never\n    : S\n\n  type OptionalCatchAllSlug<S extends string> =\n    S extends \\`\\${string}\\${SearchOrHash}\\` ? never : S\n\n  type StaticRoutes = ${staticRouteTypes || 'never'}\n  type DynamicRoutes<T extends string = string> = ${\n    dynamicRouteTypes || 'never'\n  }\n\n  type RouteImpl<T> = ${\n    routeTypesFallback ||\n    `\n    ${\n      // This keeps autocompletion working for static routes.\n      '| StaticRoutes'\n    }\n    | SearchOrHash\n    | WithProtocol\n    | \\`\\${StaticRoutes}\\${SearchOrHash}\\`\n    | (T extends \\`\\${DynamicRoutes<infer _>}\\${Suffix}\\` ? T : never)\n    `\n  }\n}\n\ndeclare module 'next' {\n  export { default } from 'next/types.js'\n  export * from 'next/types.js'\n\n  export type Route<T extends string = string> =\n    __next_route_internal_types__.RouteImpl<T>\n}\n\ndeclare module 'next/link' {\n  import type { LinkProps as OriginalLinkProps } from 'next/dist/client/link.js'\n  import type { AnchorHTMLAttributes, DetailedHTMLProps } from 'react'\n  import type { UrlObject } from 'url'\n\n  type LinkRestProps = Omit<\n    Omit<\n      DetailedHTMLProps<\n        AnchorHTMLAttributes<HTMLAnchorElement>,\n        HTMLAnchorElement\n      >,\n      keyof OriginalLinkProps\n    > &\n      OriginalLinkProps,\n    'href'\n  >\n\n  export type LinkProps<RouteInferType> = LinkRestProps & {\n    /**\n     * The path or URL to navigate to. This is the only required prop. It can also be an object.\n     * @see https://nextjs.org/docs/api-reference/next/link\n     */\n    href: __next_route_internal_types__.RouteImpl<RouteInferType> | UrlObject\n  }\n\n  export default function Link<RouteType>(props: LinkProps<RouteType>): JSX.Element\n}\n\ndeclare module 'next/navigation' {\n  export * from 'next/dist/client/components/navigation.js'\n\n  import type { NavigateOptions, AppRouterInstance as OriginalAppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime.js'\n  interface AppRouterInstance extends OriginalAppRouterInstance {\n    /**\n     * Navigate to the provided href.\n     * Pushes a new history entry.\n     */\n    push<RouteType>(href: __next_route_internal_types__.RouteImpl<RouteType>, options?: NavigateOptions): void\n    /**\n     * Navigate to the provided href.\n     * Replaces the current history entry.\n     */\n    replace<RouteType>(href: __next_route_internal_types__.RouteImpl<RouteType>, options?: NavigateOptions): void\n    /**\n     * Prefetch the provided href.\n     */\n    prefetch<RouteType>(href: __next_route_internal_types__.RouteImpl<RouteType>): void\n  }\n\n  export function useRouter(): AppRouterInstance;\n}\n\ndeclare module 'next/form' {\n  import type { FormProps as OriginalFormProps } from 'next/dist/client/form.js'\n\n  type FormRestProps = Omit<OriginalFormProps, 'action'>\n\n  export type FormProps<RouteInferType> = {\n    /**\n     * \\`action\\` can be either a \\`string\\` or a function.\n     * - If \\`action\\` is a string, it will be interpreted as a path or URL to navigate to when the form is submitted.\n     *   The path will be prefetched when the form becomes visible.\n     * - If \\`action\\` is a function, it will be called when the form is submitted. See the [React docs](https://react.dev/reference/react-dom/components/form#props) for more.\n     */\n    action: __next_route_internal_types__.RouteImpl<RouteInferType> | ((formData: FormData) => void)\n  } & FormRestProps\n\n  export default function Form<RouteType>(props: FormProps<RouteType>): JSX.Element\n}\n`\n}\n\nfunction formatTimespan(seconds: number): string {\n  if (seconds > 0) {\n    if (seconds === 18748800) {\n      return '1 month'\n    }\n    if (seconds === 18144000) {\n      return '1 month'\n    }\n    if (seconds === 604800) {\n      return '1 week'\n    }\n    if (seconds === 86400) {\n      return '1 day'\n    }\n    if (seconds === 3600) {\n      return '1 hour'\n    }\n    if (seconds === 60) {\n      return '1 minute'\n    }\n    if (seconds % 18748800 === 0) {\n      return seconds / 18748800 + ' months'\n    }\n    if (seconds % 18144000 === 0) {\n      return seconds / 18144000 + ' months'\n    }\n    if (seconds % 604800 === 0) {\n      return seconds / 604800 + ' weeks'\n    }\n    if (seconds % 86400 === 0) {\n      return seconds / 86400 + ' days'\n    }\n    if (seconds % 3600 === 0) {\n      return seconds / 3600 + ' hours'\n    }\n    if (seconds % 60 === 0) {\n      return seconds / 60 + ' minutes'\n    }\n  }\n  return seconds + ' seconds'\n}\n\nfunction formatTimespanWithSeconds(seconds: undefined | number): string {\n  if (seconds === undefined) {\n    return 'default'\n  }\n  if (seconds >= 0xfffffffe) {\n    return 'never'\n  }\n  const text = seconds + ' seconds'\n  const descriptive = formatTimespan(seconds)\n  if (descriptive === text) {\n    return text\n  }\n  return text + ' (' + descriptive + ')'\n}\n\nfunction getRootParamsFromLayouts(layouts: Record<string, string[]>) {\n  // Sort layouts by depth (descending)\n  const sortedLayouts = Object.entries(layouts).sort(\n    (a, b) => b[0].split('/').length - a[0].split('/').length\n  )\n\n  if (!sortedLayouts.length) {\n    return []\n  }\n\n  // we assume the shorted layout path is the root layout\n  let rootLayout = sortedLayouts[sortedLayouts.length - 1][0]\n\n  let rootParams = new Set<string>()\n  let isMultipleRootLayouts = false\n\n  for (const [layoutPath, params] of sortedLayouts) {\n    const allSegmentsAreDynamic = layoutPath\n      .split('/')\n      .slice(1, -1)\n      // match dynamic params but not catch-all or optional catch-all\n      .every((segment) => /^\\[[^[.\\]]+\\]$/.test(segment))\n\n    if (allSegmentsAreDynamic) {\n      if (isSubpath(rootLayout, layoutPath)) {\n        // Current path is a subpath of the root layout, update root\n        rootLayout = layoutPath\n        rootParams = new Set(params)\n      } else {\n        // Found another potential root layout\n        isMultipleRootLayouts = true\n        // Add any new params\n        for (const param of params) {\n          rootParams.add(param)\n        }\n      }\n    }\n  }\n\n  // Create result array\n  const result = Array.from(rootParams).map((param) => ({\n    param,\n    optional: isMultipleRootLayouts,\n  }))\n\n  return result\n}\n\nfunction isSubpath(parentLayoutPath: string, potentialChildLayoutPath: string) {\n  // we strip off the `layout` part of the path as those will always conflict with being a subpath\n  const parentSegments = parentLayoutPath.split('/').slice(1, -1)\n  const childSegments = potentialChildLayoutPath.split('/').slice(1, -1)\n\n  // child segments should be shorter or equal to parent segments to be a subpath\n  if (childSegments.length > parentSegments.length || !childSegments.length)\n    return false\n\n  // Verify all segment values are equal\n  return childSegments.every(\n    (childSegment, index) => childSegment === parentSegments[index]\n  )\n}\n\nfunction createServerDefinitions(\n  rootParams: { param: string; optional: boolean }[]\n) {\n  return `\n  declare module 'next/server' {\n\n    import type { AsyncLocalStorage as NodeAsyncLocalStorage } from 'async_hooks'\n    declare global {\n      var AsyncLocalStorage: typeof NodeAsyncLocalStorage\n    }\n    export { NextFetchEvent } from 'next/dist/server/web/spec-extension/fetch-event'\n    export { NextRequest } from 'next/dist/server/web/spec-extension/request'\n    export { NextResponse } from 'next/dist/server/web/spec-extension/response'\n    export { NextMiddleware, MiddlewareConfig } from 'next/dist/server/web/types'\n    export { userAgentFromString } from 'next/dist/server/web/spec-extension/user-agent'\n    export { userAgent } from 'next/dist/server/web/spec-extension/user-agent'\n    export { URLPattern } from 'next/dist/compiled/@edge-runtime/primitives/url'\n    export { ImageResponse } from 'next/dist/server/web/spec-extension/image-response'\n    export type { ImageResponseOptions } from 'next/dist/compiled/@vercel/og/types'\n    export { after } from 'next/dist/server/after'\n    export { connection } from 'next/dist/server/request/connection'\n    export type { UnsafeUnwrappedSearchParams } from 'next/dist/server/request/search-params'\n    export type { UnsafeUnwrappedParams } from 'next/dist/server/request/params'\n    export function unstable_rootParams(): Promise<{ ${rootParams\n      .map(\n        ({ param, optional }) =>\n          // ensure params with dashes are valid keys\n          `${param.includes('-') ? `'${param}'` : param}${optional ? '?' : ''}: string`\n      )\n      .join(', ')} }>\n  }\n  `\n}\n\nfunction createCustomCacheLifeDefinitions(cacheLife: {\n  [profile: string]: CacheLife\n}) {\n  let overloads = ''\n\n  const profileNames = Object.keys(cacheLife)\n  for (let i = 0; i < profileNames.length; i++) {\n    const profileName = profileNames[i]\n    const profile = cacheLife[profileName]\n    if (typeof profile !== 'object' || profile === null) {\n      continue\n    }\n\n    let description = ''\n\n    if (profile.stale === undefined) {\n      description += `\n     * This cache may be stale on clients for the default stale time of the scope before checking with the server.`\n    } else if (profile.stale >= 0xfffffffe) {\n      description += `\n     * This cache may be stale on clients indefinitely before checking with the server.`\n    } else {\n      description += `\n     * This cache may be stale on clients for ${formatTimespan(profile.stale)} before checking with the server.`\n    }\n    if (\n      profile.revalidate !== undefined &&\n      profile.expire !== undefined &&\n      profile.revalidate >= profile.expire\n    ) {\n      description += `\n     * This cache will expire after ${formatTimespan(profile.expire)}. The next request will recompute it.`\n    } else {\n      if (profile.revalidate === undefined) {\n        description += `\n     * It will inherit the default revalidate time of its scope since it does not define its own.`\n      } else if (profile.revalidate >= 0xfffffffe) {\n        // Nothing to mention.\n      } else {\n        description += `\n     * If the server receives a new request after ${formatTimespan(profile.revalidate)}, start revalidating new values in the background.`\n      }\n      if (profile.expire === undefined) {\n        description += `\n     * It will inherit the default expiration time of its scope since it does not define its own.`\n      } else if (profile.expire >= 0xfffffffe) {\n        description += `\n     * It lives for the maximum age of the server cache. If this entry has no traffic for a while, it may serve an old value the next request.`\n      } else {\n        description += `\n     * If this entry has no traffic for ${formatTimespan(profile.expire)} it will expire. The next request will recompute it.`\n      }\n    }\n\n    overloads += `\n    /**\n     * Cache this \\`\"use cache\"\\` for a timespan defined by the \\`${JSON.stringify(profileName)}\\` profile.\n     * \\`\\`\\`\n     *   stale:      ${formatTimespanWithSeconds(profile.stale)}\n     *   revalidate: ${formatTimespanWithSeconds(profile.revalidate)}\n     *   expire:     ${formatTimespanWithSeconds(profile.expire)}\n     * \\`\\`\\`\n     * ${description}\n     */\n    export function unstable_cacheLife(profile: ${JSON.stringify(profileName)}): void\n    `\n  }\n\n  overloads += `\n    /**\n     * Cache this \\`\"use cache\"\\` using a custom timespan.\n     * \\`\\`\\`\n     *   stale: ... // seconds\n     *   revalidate: ... // seconds\n     *   expire: ... // seconds\n     * \\`\\`\\`\n     *\n     * This is similar to Cache-Control: max-age=\\`stale\\`,s-max-age=\\`revalidate\\`,stale-while-revalidate=\\`expire-revalidate\\`\n     *\n     * If a value is left out, the lowest of other cacheLife() calls or the default, is used instead.\n     */\n    export function unstable_cacheLife(profile: {\n      /**\n       * This cache may be stale on clients for ... seconds before checking with the server.\n       */\n      stale?: number,\n      /**\n       * If the server receives a new request after ... seconds, start revalidating new values in the background.\n       */\n      revalidate?: number,\n      /**\n       * If this entry has no traffic for ... seconds it will expire. The next request will recompute it.\n       */\n      expire?: number\n    }): void\n  `\n\n  // Redefine the cacheLife() accepted arguments.\n  return `// Type definitions for Next.js cacheLife configs\n\ndeclare module 'next/cache' {\n  export { unstable_cache } from 'next/dist/server/web/spec-extension/unstable-cache'\n  export {\n    revalidateTag,\n    revalidatePath,\n    unstable_expireTag,\n    unstable_expirePath,\n  } from 'next/dist/server/web/spec-extension/revalidate'\n  export { unstable_noStore } from 'next/dist/server/web/spec-extension/unstable-no-store'\n\n  ${overloads}\n\n  export { cacheTag as unstable_cacheTag } from 'next/dist/server/use-cache/cache-tag'\n}\n`\n}\n\nconst appTypesBasePath = path.join('types', 'app')\n\nexport class NextTypesPlugin {\n  dir: string\n  distDir: string\n  appDir: string\n  dev: boolean\n  isEdgeServer: boolean\n  pageExtensions: string[]\n  pagesDir: string\n  typedRoutes: boolean\n  cacheLifeConfig: undefined | { [profile: string]: CacheLife }\n  distDirAbsolutePath: string\n\n  constructor(options: Options) {\n    this.dir = options.dir\n    this.distDir = options.distDir\n    this.appDir = options.appDir\n    this.dev = options.dev\n    this.isEdgeServer = options.isEdgeServer\n    this.pageExtensions = options.pageExtensions\n    this.pagesDir = path.join(this.appDir, '..', 'pages')\n    this.typedRoutes = options.typedRoutes\n    this.cacheLifeConfig = options.cacheLifeConfig\n    this.distDirAbsolutePath = path.join(this.dir, this.distDir)\n    if (this.typedRoutes && !redirectsRewritesTypesProcessed) {\n      redirectsRewritesTypesProcessed = true\n      addRedirectsRewritesRouteTypes(\n        options.originalRewrites,\n        options.originalRedirects\n      )\n    }\n  }\n\n  getRelativePathFromAppTypesDir(moduleRelativePathToAppDir: string) {\n    const moduleAbsolutePath = path.join(\n      this.appDir,\n      moduleRelativePathToAppDir\n    )\n\n    const moduleInAppTypesAbsolutePath = path.join(\n      this.distDirAbsolutePath,\n      appTypesBasePath,\n      moduleRelativePathToAppDir\n    )\n\n    return path.relative(\n      moduleInAppTypesAbsolutePath + '/..',\n      moduleAbsolutePath\n    )\n  }\n\n  collectPage(filePath: string) {\n    if (!this.typedRoutes) return\n\n    const isApp = filePath.startsWith(this.appDir + path.sep)\n    const isPages = !isApp && filePath.startsWith(this.pagesDir + path.sep)\n\n    if (!isApp && !isPages) {\n      return\n    }\n\n    // Filter out non-page and non-route files in app dir\n    if (isApp && !/[/\\\\](?:page|route)\\.[^.]+$/.test(filePath)) {\n      return\n    }\n\n    // Filter out non-page files in pages dir\n    if (\n      isPages &&\n      /[/\\\\](?:_app|_document|_error|404|500)\\.[^.]+$/.test(filePath)\n    ) {\n      return\n    }\n\n    let route = (isApp ? normalizeAppPath : denormalizePagePath)(\n      ensureLeadingSlash(\n        getPageFromPath(\n          path.relative(isApp ? this.appDir : this.pagesDir, filePath),\n          this.pageExtensions\n        )\n      )\n    )\n\n    const { isDynamic, routeType } = formatRouteToRouteType(route)\n\n    pluginState.routeTypes[this.isEdgeServer ? 'edge' : 'node'][\n      isDynamic ? 'dynamic' : 'static'\n    ] += routeType\n  }\n\n  apply(compiler: webpack.Compiler) {\n    // From asset root to dist root\n    const assetDirRelative = this.dev\n      ? '..'\n      : this.isEdgeServer\n        ? '..'\n        : '../..'\n\n    const handleModule = async (\n      mod: webpack.NormalModule,\n      compilation: webpack.Compilation\n    ) => {\n      if (!mod.resource) return\n\n      const pageExtensionsRegex = new RegExp(\n        `\\\\.(${this.pageExtensions.join('|')})$`\n      )\n\n      if (!pageExtensionsRegex.test(mod.resource)) return\n\n      if (!mod.resource.startsWith(this.appDir + path.sep)) {\n        if (!this.dev) {\n          if (mod.resource.startsWith(this.pagesDir + path.sep)) {\n            this.collectPage(mod.resource)\n          }\n        }\n        return\n      }\n      if (mod.layer !== WEBPACK_LAYERS.reactServerComponents) return\n\n      // skip for /app/_private dir convention\n      // matches <app-dir>/**/_*\n      const IS_PRIVATE = /(?:\\/[^/]+)*\\/_.*$/.test(\n        mod.resource.replace(this.appDir, '')\n      )\n      if (IS_PRIVATE) return\n\n      const IS_LAYOUT = /[/\\\\]layout\\.[^./\\\\]+$/.test(mod.resource)\n      const IS_PAGE = !IS_LAYOUT && /[/\\\\]page\\.[^.]+$/.test(mod.resource)\n      const IS_ROUTE = !IS_PAGE && /[/\\\\]route\\.[^.]+$/.test(mod.resource)\n      const IS_IMPORTABLE = /\\.(js|jsx|ts|tsx|mjs|cjs)$/.test(mod.resource)\n      const relativePathToApp = path.relative(this.appDir, mod.resource)\n\n      if (!this.dev) {\n        if (IS_PAGE || IS_ROUTE) {\n          this.collectPage(mod.resource)\n        }\n      }\n\n      const typePath = path.join(\n        appTypesBasePath,\n        relativePathToApp.replace(pageExtensionsRegex, '.ts')\n      )\n      const relativeImportPath = normalizePathSep(\n        path\n          .join(this.getRelativePathFromAppTypesDir(relativePathToApp))\n          .replace(pageExtensionsRegex, '')\n      )\n\n      const assetPath = path.join(assetDirRelative, typePath)\n\n      // Typescript won’t allow relative-importing (for example) a .mdx file using the .js extension\n      // so for now we only generate “type guard files” for files that typescript can transform\n      if (!IS_IMPORTABLE) return\n\n      if (IS_LAYOUT) {\n        const rootLayoutPath = normalizeAppPath(\n          ensureLeadingSlash(\n            getPageFromPath(\n              path.relative(this.appDir, mod.resource),\n              this.pageExtensions\n            )\n          )\n        )\n\n        const foundParams = Array.from(\n          rootLayoutPath.matchAll(/\\[(.*?)\\]/g),\n          (match) => match[1]\n        )\n\n        pluginState.collectedRootParams[rootLayoutPath] = foundParams\n\n        const slots = await collectNamedSlots(mod.resource)\n        compilation.emitAsset(\n          assetPath,\n          new sources.RawSource(\n            createTypeGuardFile(mod.resource, relativeImportPath, {\n              type: 'layout',\n              slots,\n            })\n          ) as unknown as webpack.sources.RawSource\n        )\n      } else if (IS_PAGE) {\n        compilation.emitAsset(\n          assetPath,\n          new sources.RawSource(\n            createTypeGuardFile(mod.resource, relativeImportPath, {\n              type: 'page',\n            })\n          ) as unknown as webpack.sources.RawSource\n        )\n      } else if (IS_ROUTE) {\n        compilation.emitAsset(\n          assetPath,\n          new sources.RawSource(\n            createTypeGuardFile(mod.resource, relativeImportPath, {\n              type: 'route',\n            })\n          ) as unknown as webpack.sources.RawSource\n        )\n      }\n    }\n\n    compiler.hooks.compilation.tap(PLUGIN_NAME, (compilation) => {\n      compilation.hooks.processAssets.tapAsync(\n        {\n          name: PLUGIN_NAME,\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_OPTIMIZE_HASH,\n        },\n        async (_, callback) => {\n          const promises: Promise<any>[] = []\n\n          // Clear routes\n          if (this.isEdgeServer) {\n            pluginState.routeTypes.edge.dynamic = ''\n            pluginState.routeTypes.edge.static = ''\n          } else {\n            pluginState.routeTypes.node.dynamic = ''\n            pluginState.routeTypes.node.static = ''\n          }\n\n          compilation.chunkGroups.forEach((chunkGroup) => {\n            chunkGroup.chunks.forEach((chunk) => {\n              if (!chunk.name) return\n\n              // Here we only track page and route chunks.\n              if (\n                !chunk.name.startsWith('pages/') &&\n                !(\n                  chunk.name.startsWith('app/') &&\n                  (chunk.name.endsWith('/page') ||\n                    chunk.name.endsWith('/route'))\n                )\n              ) {\n                return\n              }\n\n              const chunkModules =\n                compilation.chunkGraph.getChunkModulesIterable(\n                  chunk\n                ) as Iterable<webpack.NormalModule>\n              for (const mod of chunkModules) {\n                promises.push(handleModule(mod, compilation))\n\n                // If this is a concatenation, register each child to the parent ID.\n                const anyModule = mod as unknown as {\n                  modules: webpack.NormalModule[]\n                }\n                if (anyModule.modules) {\n                  anyModule.modules.forEach((concatenatedMod) => {\n                    promises.push(handleModule(concatenatedMod, compilation))\n                  })\n                }\n              }\n            })\n          })\n\n          await Promise.all(promises)\n\n          const rootParams = getRootParamsFromLayouts(\n            pluginState.collectedRootParams\n          )\n          // If we discovered rootParams, we'll override the `next/server` types\n          // since we're able to determine the root params at build time.\n          if (rootParams.length > 0) {\n            const serverTypesPath = path.join(\n              assetDirRelative,\n              'types/server.d.ts'\n            )\n\n            compilation.emitAsset(\n              serverTypesPath,\n              new sources.RawSource(\n                createServerDefinitions(rootParams)\n              ) as unknown as webpack.sources.RawSource\n            )\n          }\n\n          // Support `\"moduleResolution\": \"Node16\" | \"NodeNext\"` with `\"type\": \"module\"`\n\n          const packageJsonAssetPath = path.join(\n            assetDirRelative,\n            'types/package.json'\n          )\n\n          compilation.emitAsset(\n            packageJsonAssetPath,\n            new sources.RawSource(\n              '{\"type\": \"module\"}'\n            ) as unknown as webpack.sources.RawSource\n          )\n\n          if (this.typedRoutes) {\n            if (this.dev && !this.isEdgeServer) {\n              devPageFiles.forEach((file) => {\n                this.collectPage(file)\n              })\n            }\n\n            const linkAssetPath = path.join(assetDirRelative, 'types/link.d.ts')\n\n            compilation.emitAsset(\n              linkAssetPath,\n              new sources.RawSource(\n                createRouteDefinitions()\n              ) as unknown as webpack.sources.RawSource\n            )\n          }\n\n          if (this.cacheLifeConfig) {\n            const cacheLifeAssetPath = path.join(\n              assetDirRelative,\n              'types/cache-life.d.ts'\n            )\n\n            compilation.emitAsset(\n              cacheLifeAssetPath,\n              new sources.RawSource(\n                createCustomCacheLifeDefinitions(this.cacheLifeConfig)\n              ) as unknown as webpack.sources.RawSource\n            )\n          }\n\n          callback()\n        }\n      )\n    })\n  }\n}\n"], "names": ["NextTypesPlugin", "PLUGIN_NAME", "createTypeGuardFile", "fullPath", "relativePath", "options", "type", "HTTP_METHODS", "map", "method", "join", "slots", "slot", "collectNamedSlots", "<PERSON><PERSON><PERSON>", "layoutDir", "path", "dirname", "items", "fs", "readdir", "withFileTypes", "item", "isDirectory", "name", "startsWith", "push", "slice", "pluginState", "getProxiedPluginState", "collectedRootParams", "routeTypes", "edge", "static", "dynamic", "node", "extra", "formatRouteToRouteType", "route", "isDynamic", "isDynamicRoute", "split", "part", "endsWith", "routeType", "redirectsRewritesTypesProcessed", "addRedirectsRewritesRouteTypes", "rewrites", "redirects", "addExtraRoute", "source", "tokens", "parse", "Array", "isArray", "possibleNormalizedRoutes", "slugCnt", "append", "suffix", "i", "length", "fork", "<PERSON><PERSON><PERSON><PERSON>", "token", "slug", "modifier", "prefix", "pattern", "test", "normalizedRoute", "rewrite", "beforeFiles", "afterFiles", "fallback", "redirect", "createRouteDefinitions", "staticRouteTypes", "dynamicRouteTypes", "routeTypesFallback", "formatTimespan", "seconds", "formatTimespanWithSeconds", "undefined", "text", "descriptive", "getRootParamsFromLayouts", "layouts", "sortedLayouts", "Object", "entries", "sort", "a", "b", "rootLayout", "rootParams", "Set", "isMultipleRootLayouts", "params", "allSegmentsAreDynamic", "every", "segment", "isSubpath", "param", "add", "result", "from", "optional", "parentLayoutPath", "potentialChildLayoutPath", "parentSegments", "childSegments", "childSegment", "index", "createServerDefinitions", "includes", "createCustomCacheLifeDefinitions", "cacheLife", "overloads", "profileNames", "keys", "profileName", "profile", "description", "stale", "revalidate", "expire", "JSON", "stringify", "appTypesBasePath", "constructor", "dir", "distDir", "appDir", "dev", "isEdgeServer", "pageExtensions", "pagesDir", "typedRoutes", "cacheLifeConfig", "distDirAbsolutePath", "originalRewrites", "originalRedirects", "getRelativePathFromAppTypesDir", "moduleRelativePathToAppDir", "moduleAbsolutePath", "moduleInAppTypesAbsolutePath", "relative", "collectPage", "filePath", "isApp", "sep", "isPages", "normalizeAppPath", "denormalizePagePath", "ensureLeadingSlash", "getPageFromPath", "apply", "compiler", "assetDirRelative", "handleModule", "mod", "compilation", "resource", "pageExtensionsRegex", "RegExp", "layer", "WEBPACK_LAYERS", "reactServerComponents", "IS_PRIVATE", "replace", "IS_LAYOUT", "IS_PAGE", "IS_ROUTE", "IS_IMPORTABLE", "relativePathToApp", "typePath", "relativeImportPath", "normalizePathSep", "assetPath", "rootLayoutPath", "foundParams", "matchAll", "match", "emitAsset", "sources", "RawSource", "hooks", "tap", "processAssets", "tapAsync", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "_", "callback", "promises", "chunkGroups", "for<PERSON>ach", "chunkGroup", "chunks", "chunk", "chunkModules", "chunkGraph", "getChunkModulesIterable", "anyModule", "modules", "concatenatedMod", "Promise", "all", "serverTypesPath", "packageJsonAssetPath", "devPageFiles", "file", "linkAssetPath", "cacheLifeAssetPath"], "mappings": ";;;;+BAmyBaA;;;eAAAA;;;iEAhyBE;yBACkB;8BACX;6DACL;2BAEc;qCACK;oCACD;kCACF;sBACJ;uBACE;0BACE;yBACD;wBAEH;8BACS;;;;;;AAGtC,MAAMC,cAAc;AAqBpB,SAASC,oBACPC,QAAgB,EAChBC,YAAoB,EACpBC,OAGC;IAED,OAAO,CAAC,SAAS,EAAEF,SAAS;wBACN,EAAEC,aAAa;AACvC,EACEC,QAAQC,IAAI,KAAK,UACb,CAAC,iDAAiD,CAAC,GACnD,CAAC,8GAA8G,CAAC,CACrH;;6BAE4B,EAAEF,aAAa;;;;;;;;EAQ1C,EACEC,QAAQC,IAAI,KAAK,UACbC,kBAAY,CAACC,GAAG,CAAC,CAACC,SAAW,GAAGA,OAAO,WAAW,CAAC,EAAEC,IAAI,CAAC,UAC1D,oBACL;;;;;;;;;;EAUD,EACEL,QAAQC,IAAI,KAAK,UACb,KACA,CAAC;;;;;;EAMP,CAAC,CACA;;;AAGH,EAAED,QAAQC,IAAI,KAAK,UAAU,CAAC,sDAAsD,CAAC,GAAG,GAAG;AAC3F,EACED,QAAQC,IAAI,KAAK,UACbC,kBAAY,CAACC,GAAG,CACd,CAACC,SAAW,CAAC;KAChB,EAAEA,OAAO;;;;;kBAKI,EAAEA,OAAO;;qDAE0B,EAAEA,OAAO;;OAEvD,EAAEA,OAAO;;;;;;;kBAOE,EAAEA,OAAO;;sDAE2B,EAAEA,OAAO;;OAExD,EAAEA,OAAO;;;EAGd,EACE,GAID;;;;kBAIe,EAAEA,OAAO;;;;kBAIT,EAAEA,OAAO;wDAC6B,EAAEA,OAAO;;OAE1D,EAAEA,OAAO;;;;AAIhB,CAAC,EACOC,IAAI,CAAC,MACP,CAAC;iBACU,EACTL,QAAQC,IAAI,KAAK,SAAS,cAAc,cACzC;;;;mBAIY,EACfD,QAAQC,IAAI,KAAK,SAAS,cAAc,cACzC;;;;;;mBAMgB,EACfD,QAAQC,IAAI,KAAK,SAAS,cAAc,cACzC;;;AAGH,CAAC,CACA;;;;;;;;;;;;;AAaD,EACED,QAAQM,KAAK,GACTN,QAAQM,KAAK,CAACH,GAAG,CAAC,CAACI,OAAS,CAAC,EAAE,EAAEA,KAAK,iBAAiB,CAAC,EAAEF,IAAI,CAAC,QAC/D,GACL;;;;;;;;;;;;;;;;AAgBD,EACEL,QAAQC,IAAI,KAAK,UACb,CAAC;;;;CAIN,CAAC,GACI,GACL;;;;;;;;;AASD,CAAC;AACD;AAEA,eAAeO,kBAAkBC,UAAkB;IACjD,MAAMC,YAAYC,aAAI,CAACC,OAAO,CAACH;IAC/B,MAAMI,QAAQ,MAAMC,iBAAE,CAACC,OAAO,CAACL,WAAW;QAAEM,eAAe;IAAK;IAChE,MAAMV,QAAQ,EAAE;IAChB,KAAK,MAAMW,QAAQJ,MAAO;QACxB,IACEI,KAAKC,WAAW,MAChBD,KAAKE,IAAI,CAACC,UAAU,CAAC,QACrB,4GAA4G;QAC5GH,KAAKE,IAAI,KAAK,aACd;YACAb,MAAMe,IAAI,CAACJ,KAAKE,IAAI,CAACG,KAAK,CAAC;QAC7B;IACF;IACA,OAAOhB;AACT;AAEA,oEAAoE;AACpE,0EAA0E;AAC1E,8DAA8D;AAE9D,MAAMiB,cAAcC,IAAAA,mCAAqB,EAAC;IACxCC,qBAAqB,CAAC;IACtBC,YAAY;QACVC,MAAM;YACJC,QAAQ;YACRC,SAAS;QACX;QACAC,MAAM;YACJF,QAAQ;YACRC,SAAS;QACX;QACAE,OAAO;YACLH,QAAQ;YACRC,SAAS;QACX;IACF;AACF;AAEA,SAASG,uBAAuBC,KAAa;IAC3C,MAAMC,YAAYC,IAAAA,qBAAc,EAACF;IACjC,IAAIC,WAAW;QACbD,QAAQA,MACLG,KAAK,CAAC,KACNjC,GAAG,CAAC,CAACkC;YACJ,IAAIA,KAAKjB,UAAU,CAAC,QAAQiB,KAAKC,QAAQ,CAAC,MAAM;gBAC9C,IAAID,KAAKjB,UAAU,CAAC,SAAS;oBAC3B,aAAa;oBACb,OAAO,CAAC,mBAAmB,CAAC;gBAC9B,OAAO,IAAIiB,KAAKjB,UAAU,CAAC,YAAYiB,KAAKC,QAAQ,CAAC,OAAO;oBAC1D,eAAe;oBACf,OAAO,CAAC,2BAA2B,CAAC;gBACtC;gBACA,UAAU;gBACV,OAAO,CAAC,eAAe,CAAC;YAC1B;YACA,OAAOD;QACT,GACChC,IAAI,CAAC;IACV;IAEA,OAAO;QACL6B;QACAK,WAAW,CAAC,UAAU,EAAEN,MAAM,EAAE,CAAC;IACnC;AACF;AAEA,6EAA6E;AAC7E,IAAIO,kCAAkC;AAEtC,kDAAkD;AAClD,SAASC,+BACPC,QAA8B,EAC9BC,SAAiC;IAEjC,SAASC,cAAcC,MAAc;QACnC,IAAIC;QACJ,IAAI;YACFA,SAASC,IAAAA,mBAAK,EAACF;QACjB,EAAE,OAAM;QACN,gEAAgE;QAClE;QAEA,IAAIG,MAAMC,OAAO,CAACH,SAAS;YACzB,MAAMI,2BAA2B;gBAAC;aAAG;YACrC,IAAIC,UAAU;YAEd,SAASC,OAAOC,MAAc;gBAC5B,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,yBAAyBK,MAAM,EAAED,IAAK;oBACxDJ,wBAAwB,CAACI,EAAE,IAAID;gBACjC;YACF;YAEA,SAASG,KAAKH,MAAc;gBAC1B,MAAMI,gBAAgBP,yBAAyBK,MAAM;gBACrD,IAAK,IAAID,IAAI,GAAGA,IAAIG,eAAeH,IAAK;oBACtCJ,yBAAyB7B,IAAI,CAAC6B,wBAAwB,CAACI,EAAE,GAAGD;gBAC9D;YACF;YAEA,KAAK,MAAMK,SAASZ,OAAQ;gBAC1B,IAAI,OAAOY,UAAU,UAAU;oBAC7B,sCAAsC;oBACtC,MAAMC,OACJD,MAAMvC,IAAI,IAAKgC,CAAAA,cAAc,IAAI,SAAS,CAAC,IAAI,EAAEA,SAAS,AAAD;oBAE3D,IAAIO,MAAME,QAAQ,KAAK,KAAK;wBAC1BR,OAAO,GAAGM,MAAMG,MAAM,CAAC,KAAK,EAAEF,KAAK,EAAE,CAAC;oBACxC,OAAO,IAAID,MAAME,QAAQ,KAAK,KAAK;wBACjCR,OAAO,GAAGM,MAAMG,MAAM,CAAC,IAAI,EAAEF,KAAK,CAAC,CAAC;oBACtC,OAAO,IAAID,MAAME,QAAQ,KAAK,IAAI;wBAChC,IAAIF,MAAMI,OAAO,KAAK,gBAAgB;4BACpC,cAAc;4BACdV,OAAO,GAAGM,MAAMG,MAAM,CAAC,CAAC,EAAEF,KAAK,CAAC,CAAC;wBACnC,OAAO,IAAID,MAAMI,OAAO,KAAK,MAAM;4BACjC,6BAA6B;4BAC7BV,OAAO,GAAGM,MAAMG,MAAM,CAAC,KAAK,EAAEF,KAAK,EAAE,CAAC;wBACxC,OAAO,IAAID,MAAMI,OAAO,KAAK,MAAM;4BACjC,mBAAmB;4BACnBV,OAAO,GAAGM,MAAMG,MAAM,CAAC,IAAI,EAAEF,KAAK,CAAC,CAAC;wBACtC,OAAO;4BACL,2DAA2D;4BAC3D;wBACF;oBACF,OAAO,IAAID,MAAME,QAAQ,KAAK,KAAK;wBACjC,IAAI,mBAAmBG,IAAI,CAACL,MAAMI,OAAO,GAAG;4BAC1C,yDAAyD;4BACzDV,OAAOM,MAAMG,MAAM;4BACnBL,KAAKE,MAAMI,OAAO;wBACpB,OAAO;4BACL,8DAA8D;4BAC9D;wBACF;oBACF;gBACF,OAAO,IAAI,OAAOJ,UAAU,UAAU;oBACpCN,OAAOM;gBACT;YACF;YAEA,KAAK,MAAMM,mBAAmBd,yBAA0B;gBACtD,MAAM,EAAEhB,SAAS,EAAEK,SAAS,EAAE,GAAGP,uBAAuBgC;gBACxDzC,YAAYG,UAAU,CAACK,KAAK,CAACG,YAAY,YAAY,SAAS,IAC5DK;YACJ;QACF;IACF;IAEA,IAAIG,UAAU;QACZ,KAAK,MAAMuB,WAAWvB,SAASwB,WAAW,CAAE;YAC1CtB,cAAcqB,QAAQpB,MAAM;QAC9B;QACA,KAAK,MAAMoB,WAAWvB,SAASyB,UAAU,CAAE;YACzCvB,cAAcqB,QAAQpB,MAAM;QAC9B;QACA,KAAK,MAAMoB,WAAWvB,SAAS0B,QAAQ,CAAE;YACvCxB,cAAcqB,QAAQpB,MAAM;QAC9B;IACF;IAEA,IAAIF,WAAW;QACb,KAAK,MAAM0B,YAAY1B,UAAW;YAChC,0BAA0B;YAC1B,wIAAwI;YACxI,IAAI,CAAE,CAAA,cAAc0B,QAAO,GAAI;gBAC7BzB,cAAcyB,SAASxB,MAAM;YAC/B;QACF;IACF;AACF;AAEA,SAASyB;IACP,IAAIC,mBAAmB;IACvB,IAAIC,oBAAoB;IAExB,KAAK,MAAMvE,QAAQ;QAAC;QAAQ;QAAQ;KAAQ,CAAW;QACrDsE,oBAAoBhD,YAAYG,UAAU,CAACzB,KAAK,CAAC2B,MAAM;QACvD4C,qBAAqBjD,YAAYG,UAAU,CAACzB,KAAK,CAAC4B,OAAO;IAC3D;IAEA,oFAAoF;IACpF,MAAM4C,qBACJ,CAACF,oBAAoB,CAACC,oBAAoB,gBAAgB;IAE5D,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sBA8BY,EAAED,oBAAoB,QAAQ;kDACF,EAC9CC,qBAAqB,QACtB;;sBAEmB,EAClBC,sBACA,CAAC;IACD,EACE,uDAAuD;IACvD,iBACD;;;;;IAKD,CAAC,CACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFH,CAAC;AACD;AAEA,SAASC,eAAeC,OAAe;IACrC,IAAIA,UAAU,GAAG;QACf,IAAIA,YAAY,UAAU;YACxB,OAAO;QACT;QACA,IAAIA,YAAY,UAAU;YACxB,OAAO;QACT;QACA,IAAIA,YAAY,QAAQ;YACtB,OAAO;QACT;QACA,IAAIA,YAAY,OAAO;YACrB,OAAO;QACT;QACA,IAAIA,YAAY,MAAM;YACpB,OAAO;QACT;QACA,IAAIA,YAAY,IAAI;YAClB,OAAO;QACT;QACA,IAAIA,UAAU,aAAa,GAAG;YAC5B,OAAOA,UAAU,WAAW;QAC9B;QACA,IAAIA,UAAU,aAAa,GAAG;YAC5B,OAAOA,UAAU,WAAW;QAC9B;QACA,IAAIA,UAAU,WAAW,GAAG;YAC1B,OAAOA,UAAU,SAAS;QAC5B;QACA,IAAIA,UAAU,UAAU,GAAG;YACzB,OAAOA,UAAU,QAAQ;QAC3B;QACA,IAAIA,UAAU,SAAS,GAAG;YACxB,OAAOA,UAAU,OAAO;QAC1B;QACA,IAAIA,UAAU,OAAO,GAAG;YACtB,OAAOA,UAAU,KAAK;QACxB;IACF;IACA,OAAOA,UAAU;AACnB;AAEA,SAASC,0BAA0BD,OAA2B;IAC5D,IAAIA,YAAYE,WAAW;QACzB,OAAO;IACT;IACA,IAAIF,WAAW,YAAY;QACzB,OAAO;IACT;IACA,MAAMG,OAAOH,UAAU;IACvB,MAAMI,cAAcL,eAAeC;IACnC,IAAII,gBAAgBD,MAAM;QACxB,OAAOA;IACT;IACA,OAAOA,OAAO,OAAOC,cAAc;AACrC;AAEA,SAASC,yBAAyBC,OAAiC;IACjE,qCAAqC;IACrC,MAAMC,gBAAgBC,OAAOC,OAAO,CAACH,SAASI,IAAI,CAChD,CAACC,GAAGC,IAAMA,CAAC,CAAC,EAAE,CAACnD,KAAK,CAAC,KAAKmB,MAAM,GAAG+B,CAAC,CAAC,EAAE,CAAClD,KAAK,CAAC,KAAKmB,MAAM;IAG3D,IAAI,CAAC2B,cAAc3B,MAAM,EAAE;QACzB,OAAO,EAAE;IACX;IAEA,uDAAuD;IACvD,IAAIiC,aAAaN,aAAa,CAACA,cAAc3B,MAAM,GAAG,EAAE,CAAC,EAAE;IAE3D,IAAIkC,aAAa,IAAIC;IACrB,IAAIC,wBAAwB;IAE5B,KAAK,MAAM,CAAClF,YAAYmF,OAAO,IAAIV,cAAe;QAChD,MAAMW,wBAAwBpF,WAC3B2B,KAAK,CAAC,KACNd,KAAK,CAAC,GAAG,CAAC,EACX,+DAA+D;SAC9DwE,KAAK,CAAC,CAACC,UAAY,iBAAiBhC,IAAI,CAACgC;QAE5C,IAAIF,uBAAuB;YACzB,IAAIG,UAAUR,YAAY/E,aAAa;gBACrC,4DAA4D;gBAC5D+E,aAAa/E;gBACbgF,aAAa,IAAIC,IAAIE;YACvB,OAAO;gBACL,sCAAsC;gBACtCD,wBAAwB;gBACxB,qBAAqB;gBACrB,KAAK,MAAMM,SAASL,OAAQ;oBAC1BH,WAAWS,GAAG,CAACD;gBACjB;YACF;QACF;IACF;IAEA,sBAAsB;IACtB,MAAME,SAASnD,MAAMoD,IAAI,CAACX,YAAYtF,GAAG,CAAC,CAAC8F,QAAW,CAAA;YACpDA;YACAI,UAAUV;QACZ,CAAA;IAEA,OAAOQ;AACT;AAEA,SAASH,UAAUM,gBAAwB,EAAEC,wBAAgC;IAC3E,gGAAgG;IAChG,MAAMC,iBAAiBF,iBAAiBlE,KAAK,CAAC,KAAKd,KAAK,CAAC,GAAG,CAAC;IAC7D,MAAMmF,gBAAgBF,yBAAyBnE,KAAK,CAAC,KAAKd,KAAK,CAAC,GAAG,CAAC;IAEpE,+EAA+E;IAC/E,IAAImF,cAAclD,MAAM,GAAGiD,eAAejD,MAAM,IAAI,CAACkD,cAAclD,MAAM,EACvE,OAAO;IAET,sCAAsC;IACtC,OAAOkD,cAAcX,KAAK,CACxB,CAACY,cAAcC,QAAUD,iBAAiBF,cAAc,CAACG,MAAM;AAEnE;AAEA,SAASC,wBACPnB,UAAkD;IAElD,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;qDAoB2C,EAAEA,WAChDtF,GAAG,CACF,CAAC,EAAE8F,KAAK,EAAEI,QAAQ,EAAE,GAClB,2CAA2C;QAC3C,GAAGJ,MAAMY,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAEZ,MAAM,CAAC,CAAC,GAAGA,QAAQI,WAAW,MAAM,GAAG,QAAQ,CAAC,EAEhFhG,IAAI,CAAC,MAAM;;EAEhB,CAAC;AACH;AAEA,SAASyG,iCAAiCC,SAEzC;IACC,IAAIC,YAAY;IAEhB,MAAMC,eAAe9B,OAAO+B,IAAI,CAACH;IACjC,IAAK,IAAIzD,IAAI,GAAGA,IAAI2D,aAAa1D,MAAM,EAAED,IAAK;QAC5C,MAAM6D,cAAcF,YAAY,CAAC3D,EAAE;QACnC,MAAM8D,UAAUL,SAAS,CAACI,YAAY;QACtC,IAAI,OAAOC,YAAY,YAAYA,YAAY,MAAM;YACnD;QACF;QAEA,IAAIC,cAAc;QAElB,IAAID,QAAQE,KAAK,KAAKzC,WAAW;YAC/BwC,eAAe,CAAC;kHAC4F,CAAC;QAC/G,OAAO,IAAID,QAAQE,KAAK,IAAI,YAAY;YACtCD,eAAe,CAAC;uFACiE,CAAC;QACpF,OAAO;YACLA,eAAe,CAAC;8CACwB,EAAE3C,eAAe0C,QAAQE,KAAK,EAAE,iCAAiC,CAAC;QAC5G;QACA,IACEF,QAAQG,UAAU,KAAK1C,aACvBuC,QAAQI,MAAM,KAAK3C,aACnBuC,QAAQG,UAAU,IAAIH,QAAQI,MAAM,EACpC;YACAH,eAAe,CAAC;oCACc,EAAE3C,eAAe0C,QAAQI,MAAM,EAAE,qCAAqC,CAAC;QACvG,OAAO;YACL,IAAIJ,QAAQG,UAAU,KAAK1C,WAAW;gBACpCwC,eAAe,CAAC;iGACyE,CAAC;YAC5F,OAAO,IAAID,QAAQG,UAAU,IAAI,YAAY;YAC3C,sBAAsB;YACxB,OAAO;gBACLF,eAAe,CAAC;kDAC0B,EAAE3C,eAAe0C,QAAQG,UAAU,EAAE,kDAAkD,CAAC;YACpI;YACA,IAAIH,QAAQI,MAAM,KAAK3C,WAAW;gBAChCwC,eAAe,CAAC;iGACyE,CAAC;YAC5F,OAAO,IAAID,QAAQI,MAAM,IAAI,YAAY;gBACvCH,eAAe,CAAC;8IACsH,CAAC;YACzI,OAAO;gBACLA,eAAe,CAAC;wCACgB,EAAE3C,eAAe0C,QAAQI,MAAM,EAAE,oDAAoD,CAAC;YACxH;QACF;QAEAR,aAAa,CAAC;;kEAEgD,EAAES,KAAKC,SAAS,CAACP,aAAa;;qBAE3E,EAAEvC,0BAA0BwC,QAAQE,KAAK,EAAE;qBAC3C,EAAE1C,0BAA0BwC,QAAQG,UAAU,EAAE;qBAChD,EAAE3C,0BAA0BwC,QAAQI,MAAM,EAAE;;OAE1D,EAAEH,YAAY;;gDAE2B,EAAEI,KAAKC,SAAS,CAACP,aAAa;IAC1E,CAAC;IACH;IAEAH,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;EA2Bd,CAAC;IAED,+CAA+C;IAC/C,OAAO,CAAC;;;;;;;;;;;;EAYR,EAAEA,UAAU;;;;AAId,CAAC;AACD;AAEA,MAAMW,mBAAmBhH,aAAI,CAACN,IAAI,CAAC,SAAS;AAErC,MAAMV;IAYXiI,YAAY5H,OAAgB,CAAE;QAC5B,IAAI,CAAC6H,GAAG,GAAG7H,QAAQ6H,GAAG;QACtB,IAAI,CAACC,OAAO,GAAG9H,QAAQ8H,OAAO;QAC9B,IAAI,CAACC,MAAM,GAAG/H,QAAQ+H,MAAM;QAC5B,IAAI,CAACC,GAAG,GAAGhI,QAAQgI,GAAG;QACtB,IAAI,CAACC,YAAY,GAAGjI,QAAQiI,YAAY;QACxC,IAAI,CAACC,cAAc,GAAGlI,QAAQkI,cAAc;QAC5C,IAAI,CAACC,QAAQ,GAAGxH,aAAI,CAACN,IAAI,CAAC,IAAI,CAAC0H,MAAM,EAAE,MAAM;QAC7C,IAAI,CAACK,WAAW,GAAGpI,QAAQoI,WAAW;QACtC,IAAI,CAACC,eAAe,GAAGrI,QAAQqI,eAAe;QAC9C,IAAI,CAACC,mBAAmB,GAAG3H,aAAI,CAACN,IAAI,CAAC,IAAI,CAACwH,GAAG,EAAE,IAAI,CAACC,OAAO;QAC3D,IAAI,IAAI,CAACM,WAAW,IAAI,CAAC5F,iCAAiC;YACxDA,kCAAkC;YAClCC,+BACEzC,QAAQuI,gBAAgB,EACxBvI,QAAQwI,iBAAiB;QAE7B;IACF;IAEAC,+BAA+BC,0BAAkC,EAAE;QACjE,MAAMC,qBAAqBhI,aAAI,CAACN,IAAI,CAClC,IAAI,CAAC0H,MAAM,EACXW;QAGF,MAAME,+BAA+BjI,aAAI,CAACN,IAAI,CAC5C,IAAI,CAACiI,mBAAmB,EACxBX,kBACAe;QAGF,OAAO/H,aAAI,CAACkI,QAAQ,CAClBD,+BAA+B,OAC/BD;IAEJ;IAEAG,YAAYC,QAAgB,EAAE;QAC5B,IAAI,CAAC,IAAI,CAACX,WAAW,EAAE;QAEvB,MAAMY,QAAQD,SAAS3H,UAAU,CAAC,IAAI,CAAC2G,MAAM,GAAGpH,aAAI,CAACsI,GAAG;QACxD,MAAMC,UAAU,CAACF,SAASD,SAAS3H,UAAU,CAAC,IAAI,CAAC+G,QAAQ,GAAGxH,aAAI,CAACsI,GAAG;QAEtE,IAAI,CAACD,SAAS,CAACE,SAAS;YACtB;QACF;QAEA,qDAAqD;QACrD,IAAIF,SAAS,CAAC,8BAA8BjF,IAAI,CAACgF,WAAW;YAC1D;QACF;QAEA,yCAAyC;QACzC,IACEG,WACA,iDAAiDnF,IAAI,CAACgF,WACtD;YACA;QACF;QAEA,IAAI9G,QAAQ,AAAC+G,CAAAA,QAAQG,0BAAgB,GAAGC,wCAAmB,AAAD,EACxDC,IAAAA,sCAAkB,EAChBC,IAAAA,wBAAe,EACb3I,aAAI,CAACkI,QAAQ,CAACG,QAAQ,IAAI,CAACjB,MAAM,GAAG,IAAI,CAACI,QAAQ,EAAEY,WACnD,IAAI,CAACb,cAAc;QAKzB,MAAM,EAAEhG,SAAS,EAAEK,SAAS,EAAE,GAAGP,uBAAuBC;QAExDV,YAAYG,UAAU,CAAC,IAAI,CAACuG,YAAY,GAAG,SAAS,OAAO,CACzD/F,YAAY,YAAY,SACzB,IAAIK;IACP;IAEAgH,MAAMC,QAA0B,EAAE;QAChC,+BAA+B;QAC/B,MAAMC,mBAAmB,IAAI,CAACzB,GAAG,GAC7B,OACA,IAAI,CAACC,YAAY,GACf,OACA;QAEN,MAAMyB,eAAe,OACnBC,KACAC;YAEA,IAAI,CAACD,IAAIE,QAAQ,EAAE;YAEnB,MAAMC,sBAAsB,IAAIC,OAC9B,CAAC,IAAI,EAAE,IAAI,CAAC7B,cAAc,CAAC7H,IAAI,CAAC,KAAK,EAAE,CAAC;YAG1C,IAAI,CAACyJ,oBAAoB/F,IAAI,CAAC4F,IAAIE,QAAQ,GAAG;YAE7C,IAAI,CAACF,IAAIE,QAAQ,CAACzI,UAAU,CAAC,IAAI,CAAC2G,MAAM,GAAGpH,aAAI,CAACsI,GAAG,GAAG;gBACpD,IAAI,CAAC,IAAI,CAACjB,GAAG,EAAE;oBACb,IAAI2B,IAAIE,QAAQ,CAACzI,UAAU,CAAC,IAAI,CAAC+G,QAAQ,GAAGxH,aAAI,CAACsI,GAAG,GAAG;wBACrD,IAAI,CAACH,WAAW,CAACa,IAAIE,QAAQ;oBAC/B;gBACF;gBACA;YACF;YACA,IAAIF,IAAIK,KAAK,KAAKC,yBAAc,CAACC,qBAAqB,EAAE;YAExD,wCAAwC;YACxC,0BAA0B;YAC1B,MAAMC,aAAa,qBAAqBpG,IAAI,CAC1C4F,IAAIE,QAAQ,CAACO,OAAO,CAAC,IAAI,CAACrC,MAAM,EAAE;YAEpC,IAAIoC,YAAY;YAEhB,MAAME,YAAY,yBAAyBtG,IAAI,CAAC4F,IAAIE,QAAQ;YAC5D,MAAMS,UAAU,CAACD,aAAa,oBAAoBtG,IAAI,CAAC4F,IAAIE,QAAQ;YACnE,MAAMU,WAAW,CAACD,WAAW,qBAAqBvG,IAAI,CAAC4F,IAAIE,QAAQ;YACnE,MAAMW,gBAAgB,6BAA6BzG,IAAI,CAAC4F,IAAIE,QAAQ;YACpE,MAAMY,oBAAoB9J,aAAI,CAACkI,QAAQ,CAAC,IAAI,CAACd,MAAM,EAAE4B,IAAIE,QAAQ;YAEjE,IAAI,CAAC,IAAI,CAAC7B,GAAG,EAAE;gBACb,IAAIsC,WAAWC,UAAU;oBACvB,IAAI,CAACzB,WAAW,CAACa,IAAIE,QAAQ;gBAC/B;YACF;YAEA,MAAMa,WAAW/J,aAAI,CAACN,IAAI,CACxBsH,kBACA8C,kBAAkBL,OAAO,CAACN,qBAAqB;YAEjD,MAAMa,qBAAqBC,IAAAA,kCAAgB,EACzCjK,aAAI,CACDN,IAAI,CAAC,IAAI,CAACoI,8BAA8B,CAACgC,oBACzCL,OAAO,CAACN,qBAAqB;YAGlC,MAAMe,YAAYlK,aAAI,CAACN,IAAI,CAACoJ,kBAAkBiB;YAE9C,8FAA8F;YAC9F,yFAAyF;YACzF,IAAI,CAACF,eAAe;YAEpB,IAAIH,WAAW;gBACb,MAAMS,iBAAiB3B,IAAAA,0BAAgB,EACrCE,IAAAA,sCAAkB,EAChBC,IAAAA,wBAAe,EACb3I,aAAI,CAACkI,QAAQ,CAAC,IAAI,CAACd,MAAM,EAAE4B,IAAIE,QAAQ,GACvC,IAAI,CAAC3B,cAAc;gBAKzB,MAAM6C,cAAc/H,MAAMoD,IAAI,CAC5B0E,eAAeE,QAAQ,CAAC,eACxB,CAACC,QAAUA,KAAK,CAAC,EAAE;gBAGrB1J,YAAYE,mBAAmB,CAACqJ,eAAe,GAAGC;gBAElD,MAAMzK,QAAQ,MAAME,kBAAkBmJ,IAAIE,QAAQ;gBAClDD,YAAYsB,SAAS,CACnBL,WACA,IAAIM,gBAAO,CAACC,SAAS,CACnBvL,oBAAoB8J,IAAIE,QAAQ,EAAEc,oBAAoB;oBACpD1K,MAAM;oBACNK;gBACF;YAGN,OAAO,IAAIgK,SAAS;gBAClBV,YAAYsB,SAAS,CACnBL,WACA,IAAIM,gBAAO,CAACC,SAAS,CACnBvL,oBAAoB8J,IAAIE,QAAQ,EAAEc,oBAAoB;oBACpD1K,MAAM;gBACR;YAGN,OAAO,IAAIsK,UAAU;gBACnBX,YAAYsB,SAAS,CACnBL,WACA,IAAIM,gBAAO,CAACC,SAAS,CACnBvL,oBAAoB8J,IAAIE,QAAQ,EAAEc,oBAAoB;oBACpD1K,MAAM;gBACR;YAGN;QACF;QAEAuJ,SAAS6B,KAAK,CAACzB,WAAW,CAAC0B,GAAG,CAAC1L,aAAa,CAACgK;YAC3CA,YAAYyB,KAAK,CAACE,aAAa,CAACC,QAAQ,CACtC;gBACErK,MAAMvB;gBACN6L,OAAOC,gBAAO,CAACC,WAAW,CAACC,kCAAkC;YAC/D,GACA,OAAOC,GAAGC;gBACR,MAAMC,WAA2B,EAAE;gBAEnC,eAAe;gBACf,IAAI,IAAI,CAAC9D,YAAY,EAAE;oBACrB1G,YAAYG,UAAU,CAACC,IAAI,CAACE,OAAO,GAAG;oBACtCN,YAAYG,UAAU,CAACC,IAAI,CAACC,MAAM,GAAG;gBACvC,OAAO;oBACLL,YAAYG,UAAU,CAACI,IAAI,CAACD,OAAO,GAAG;oBACtCN,YAAYG,UAAU,CAACI,IAAI,CAACF,MAAM,GAAG;gBACvC;gBAEAgI,YAAYoC,WAAW,CAACC,OAAO,CAAC,CAACC;oBAC/BA,WAAWC,MAAM,CAACF,OAAO,CAAC,CAACG;wBACzB,IAAI,CAACA,MAAMjL,IAAI,EAAE;wBAEjB,4CAA4C;wBAC5C,IACE,CAACiL,MAAMjL,IAAI,CAACC,UAAU,CAAC,aACvB,CACEgL,CAAAA,MAAMjL,IAAI,CAACC,UAAU,CAAC,WACrBgL,CAAAA,MAAMjL,IAAI,CAACmB,QAAQ,CAAC,YACnB8J,MAAMjL,IAAI,CAACmB,QAAQ,CAAC,SAAQ,CAAC,GAEjC;4BACA;wBACF;wBAEA,MAAM+J,eACJzC,YAAY0C,UAAU,CAACC,uBAAuB,CAC5CH;wBAEJ,KAAK,MAAMzC,OAAO0C,aAAc;4BAC9BN,SAAS1K,IAAI,CAACqI,aAAaC,KAAKC;4BAEhC,oEAAoE;4BACpE,MAAM4C,YAAY7C;4BAGlB,IAAI6C,UAAUC,OAAO,EAAE;gCACrBD,UAAUC,OAAO,CAACR,OAAO,CAAC,CAACS;oCACzBX,SAAS1K,IAAI,CAACqI,aAAagD,iBAAiB9C;gCAC9C;4BACF;wBACF;oBACF;gBACF;gBAEA,MAAM+C,QAAQC,GAAG,CAACb;gBAElB,MAAMtG,aAAaT,yBACjBzD,YAAYE,mBAAmB;gBAEjC,sEAAsE;gBACtE,+DAA+D;gBAC/D,IAAIgE,WAAWlC,MAAM,GAAG,GAAG;oBACzB,MAAMsJ,kBAAkBlM,aAAI,CAACN,IAAI,CAC/BoJ,kBACA;oBAGFG,YAAYsB,SAAS,CACnB2B,iBACA,IAAI1B,gBAAO,CAACC,SAAS,CACnBxE,wBAAwBnB;gBAG9B;gBAEA,8EAA8E;gBAE9E,MAAMqH,uBAAuBnM,aAAI,CAACN,IAAI,CACpCoJ,kBACA;gBAGFG,YAAYsB,SAAS,CACnB4B,sBACA,IAAI3B,gBAAO,CAACC,SAAS,CACnB;gBAIJ,IAAI,IAAI,CAAChD,WAAW,EAAE;oBACpB,IAAI,IAAI,CAACJ,GAAG,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;wBAClC8E,oBAAY,CAACd,OAAO,CAAC,CAACe;4BACpB,IAAI,CAAClE,WAAW,CAACkE;wBACnB;oBACF;oBAEA,MAAMC,gBAAgBtM,aAAI,CAACN,IAAI,CAACoJ,kBAAkB;oBAElDG,YAAYsB,SAAS,CACnB+B,eACA,IAAI9B,gBAAO,CAACC,SAAS,CACnB9G;gBAGN;gBAEA,IAAI,IAAI,CAAC+D,eAAe,EAAE;oBACxB,MAAM6E,qBAAqBvM,aAAI,CAACN,IAAI,CAClCoJ,kBACA;oBAGFG,YAAYsB,SAAS,CACnBgC,oBACA,IAAI/B,gBAAO,CAACC,SAAS,CACnBtE,iCAAiC,IAAI,CAACuB,eAAe;gBAG3D;gBAEAyD;YACF;QAEJ;IACF;AACF"}