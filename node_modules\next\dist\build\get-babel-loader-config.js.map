{"version": 3, "sources": ["../../src/build/get-babel-loader-config.ts"], "sourcesContent": ["import path from 'path'\nimport type { ReactCompilerOptions } from '../server/config-shared'\n\nfunction getReactCompiler() {\n  try {\n    // It's in peerDependencies, so it should be available\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    return require.resolve('babel-plugin-react-compiler')\n  } catch {\n    throw new Error(\n      'Failed to load the `babel-plugin-react-compiler`. It is required to use the React Compiler. Please install it.'\n    )\n  }\n}\n\nconst getReactCompilerPlugins = (\n  options: boolean | ReactCompilerOptions | undefined,\n  isDev: boolean,\n  isServer: boolean\n) => {\n  if (!options || isServer) {\n    return undefined\n  }\n\n  const compilerOptions = typeof options === 'boolean' ? {} : options\n  if (options) {\n    return [\n      [\n        getReactCompiler(),\n        {\n          panicThreshold: isDev ? undefined : 'NONE',\n          ...compilerOptions,\n        },\n      ],\n    ]\n  }\n  return undefined\n}\n\nconst getBabelLoader = (\n  useSWCLoader: boolean | undefined,\n  babelConfigFile: string | undefined,\n  isServer: boolean,\n  distDir: string,\n  pagesDir: string | undefined,\n  cwd: string,\n  srcDir: string,\n  dev: boolean,\n  isClient: boolean,\n  reactCompilerOptions: boolean | ReactCompilerOptions | undefined,\n  reactCompilerExclude: ((excludePath: string) => boolean) | undefined\n) => {\n  if (!useSWCLoader) {\n    return {\n      loader: require.resolve('./babel/loader/index'),\n      options: {\n        transformMode: 'default',\n        configFile: babelConfigFile,\n        isServer,\n        distDir,\n        pagesDir,\n        cwd,\n        srcDir: path.dirname(srcDir),\n        development: dev,\n        hasReactRefresh: dev && isClient,\n        hasJsxRuntime: true,\n        reactCompilerPlugins: getReactCompilerPlugins(\n          reactCompilerOptions,\n          dev,\n          isServer\n        ),\n        reactCompilerExclude,\n      },\n    }\n  }\n\n  return undefined\n}\n\n/**\n * Get a separate babel loader for the react compiler, only used if Babel is not\n * configured through e.g. .babelrc. If user have babel config, this should be configured in the babel loader itself.\n * Note from react compiler:\n * > For best results, compiler must run as the first plugin in your Babel pipeline so it receives input as close to the original source as possible.\n */\nconst getReactCompilerLoader = (\n  options: boolean | ReactCompilerOptions | undefined,\n  cwd: string,\n  isDev: boolean,\n  isServer: boolean,\n  reactCompilerExclude: ((excludePath: string) => boolean) | undefined\n) => {\n  const reactCompilerPlugins = getReactCompilerPlugins(options, isDev, isServer)\n  if (!reactCompilerPlugins) {\n    return undefined\n  }\n\n  const config: any = {\n    loader: require.resolve('./babel/loader/index'),\n    options: {\n      transformMode: 'standalone',\n      cwd,\n      reactCompilerPlugins,\n    },\n  }\n\n  if (reactCompilerExclude) {\n    config.options.reactCompilerExclude = reactCompilerExclude\n  }\n\n  return config\n}\n\nexport { getBabelLoader, getReactCompilerLoader }\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getReactCompilerLoader", "getReactCompiler", "require", "resolve", "Error", "getReactCompilerPlugins", "options", "isDev", "isServer", "undefined", "compilerOptions", "panicT<PERSON>eshold", "useSWCLoader", "babelConfigFile", "distDir", "pagesDir", "cwd", "srcDir", "dev", "isClient", "reactCompilerOptions", "reactCompilerExclude", "loader", "transformMode", "configFile", "path", "dirname", "development", "hasReactRefresh", "hasJsxRuntime", "reactCompilerPlugins", "config"], "mappings": ";;;;;;;;;;;;;;;IAiHSA,cAAc;eAAdA;;IAAgBC,sBAAsB;eAAtBA;;;6DAjHR;;;;;;AAGjB,SAASC;IACP,IAAI;QACF,sDAAsD;QACtD,6DAA6D;QAC7D,OAAOC,QAAQC,OAAO,CAAC;IACzB,EAAE,OAAM;QACN,MAAM,qBAEL,CAFK,IAAIC,MACR,mHADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAEA,MAAMC,0BAA0B,CAC9BC,SACAC,OACAC;IAEA,IAAI,CAACF,WAAWE,UAAU;QACxB,OAAOC;IACT;IAEA,MAAMC,kBAAkB,OAAOJ,YAAY,YAAY,CAAC,IAAIA;IAC5D,IAAIA,SAAS;QACX,OAAO;YACL;gBACEL;gBACA;oBACEU,gBAAgBJ,QAAQE,YAAY;oBACpC,GAAGC,eAAe;gBACpB;aACD;SACF;IACH;IACA,OAAOD;AACT;AAEA,MAAMV,iBAAiB,CACrBa,cACAC,iBACAL,UACAM,SACAC,UACAC,KACAC,QACAC,KACAC,UACAC,sBACAC;IAEA,IAAI,CAACT,cAAc;QACjB,OAAO;YACLU,QAAQpB,QAAQC,OAAO,CAAC;YACxBG,SAAS;gBACPiB,eAAe;gBACfC,YAAYX;gBACZL;gBACAM;gBACAC;gBACAC;gBACAC,QAAQQ,aAAI,CAACC,OAAO,CAACT;gBACrBU,aAAaT;gBACbU,iBAAiBV,OAAOC;gBACxBU,eAAe;gBACfC,sBAAsBzB,wBACpBe,sBACAF,KACAV;gBAEFa;YACF;QACF;IACF;IAEA,OAAOZ;AACT;AAEA;;;;;CAKC,GACD,MAAMT,yBAAyB,CAC7BM,SACAU,KACAT,OACAC,UACAa;IAEA,MAAMS,uBAAuBzB,wBAAwBC,SAASC,OAAOC;IACrE,IAAI,CAACsB,sBAAsB;QACzB,OAAOrB;IACT;IAEA,MAAMsB,SAAc;QAClBT,QAAQpB,QAAQC,OAAO,CAAC;QACxBG,SAAS;YACPiB,eAAe;YACfP;YACAc;QACF;IACF;IAEA,IAAIT,sBAAsB;QACxBU,OAAOzB,OAAO,CAACe,oBAAoB,GAAGA;IACxC;IAEA,OAAOU;AACT"}