import { NextRequest, NextResponse } from 'next/server';
import { urlStore } from '@/lib/urlStore';

// Generate a random short code
function generateShortCode(): string {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Validate URL format
function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch {
    return false;
  }
}

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json({ error: 'URL is required' }, { status: 400 });
    }

    if (!isValidUrl(url)) {
      return NextResponse.json({ error: 'Invalid URL format' }, { status: 400 });
    }

    // Generate unique short code
    let shortCode = generateShortCode();
    while (urlStore.has(shortCode)) {
      shortCode = generateShortCode();
    }

    // Store in database
    urlStore.set(shortCode, {
      originalUrl: url,
      clicks: 0,
      createdAt: new Date()
    });

    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || request.nextUrl.origin;
    const shortUrl = `${baseUrl}/s/${shortCode}`;

    return NextResponse.json({
      shortUrl,
      shortCode,
      originalUrl: url
    });

  } catch (error) {
    console.error('Error shortening URL:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET() {
  // Return recent links for the dashboard
  const recentLinks = urlStore.getAll()
    .map((data) => ({
      ...data,
      shortUrl: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/s/${data.shortCode}`
    }))
    .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
    .slice(0, 10);

  return NextResponse.json({ links: recentLinks });
}
