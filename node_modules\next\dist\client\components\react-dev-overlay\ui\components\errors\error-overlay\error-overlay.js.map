{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/error-overlay/error-overlay.tsx"], "sourcesContent": ["import type { OverlayState } from '../../../../shared'\n\nimport { Suspense } from 'react'\nimport { BuildError } from '../../../container/build-error'\nimport { Errors } from '../../../container/errors'\nimport { useDelayedRender } from '../../../hooks/use-delayed-render'\nimport type { ReadyRuntimeError } from '../../../../utils/get-error-by-type'\n\nconst transitionDurationMs = 200\n\nexport interface ErrorBaseProps {\n  rendered: boolean\n  transitionDurationMs: number\n  isTurbopack: boolean\n  versionInfo: OverlayState['versionInfo']\n}\n\nexport function ErrorOverlay({\n  state,\n  runtimeErrors,\n  isErrorOverlayOpen,\n  setIsErrorOverlayOpen,\n}: {\n  state: OverlayState\n  runtimeErrors: ReadyRuntimeError[]\n  isErrorOverlayOpen: boolean\n  setIsErrorOverlayOpen: (value: boolean) => void\n}) {\n  const isTurbopack = !!process.env.TURBOPACK\n\n  // This hook lets us do an exit animation before unmounting the component\n  const { mounted, rendered } = useDelayedRender(isErrorOverlayOpen, {\n    exitDelay: transitionDurationMs,\n  })\n\n  const commonProps = {\n    rendered,\n    transitionDurationMs,\n    isTurbopack,\n    versionInfo: state.versionInfo,\n  }\n\n  if (state.buildError !== null) {\n    return (\n      <BuildError\n        {...commonProps}\n        message={state.buildError}\n        // This is not a runtime error, forcedly display error overlay\n        rendered\n      />\n    )\n  }\n\n  // No Runtime Errors.\n  if (!runtimeErrors.length) {\n    // Workaround React quirk that triggers \"Switch to client-side rendering\" if\n    // we return no Suspense boundary here.\n    return <Suspense />\n  }\n\n  if (!mounted) {\n    // Workaround React quirk that triggers \"Switch to client-side rendering\" if\n    // we return no Suspense boundary here.\n    return <Suspense />\n  }\n\n  return (\n    <Errors\n      {...commonProps}\n      debugInfo={state.debugInfo}\n      runtimeErrors={runtimeErrors}\n      onClose={() => {\n        setIsErrorOverlayOpen(false)\n      }}\n    />\n  )\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transitionDurationMs", "state", "runtimeErrors", "isErrorOverlayOpen", "setIsErrorOverlayOpen", "isTurbopack", "process", "env", "TURBOPACK", "mounted", "rendered", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exitDelay", "commonProps", "versionInfo", "buildError", "BuildError", "message", "length", "Suspense", "Errors", "debugInfo", "onClose"], "mappings": ";;;;+BAiBgBA;;;eAAAA;;;;uBAfS;4BACE;wBACJ;kCACU;AAGjC,MAAMC,uBAAuB;AAStB,SAASD,aAAa,KAU5B;IAV4B,IAAA,EAC3BE,KAAK,EACLC,aAAa,EACbC,kBAAkB,EAClBC,qBAAqB,EAMtB,GAV4B;IAW3B,MAAMC,cAAc,CAAC,CAACC,QAAQC,GAAG,CAACC,SAAS;IAE3C,yEAAyE;IACzE,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAE,GAAGC,IAAAA,kCAAgB,EAACR,oBAAoB;QACjES,WAAWZ;IACb;IAEA,MAAMa,cAAc;QAClBH;QACAV;QACAK;QACAS,aAAab,MAAMa,WAAW;IAChC;IAEA,IAAIb,MAAMc,UAAU,KAAK,MAAM;QAC7B,qBACE,qBAACC,sBAAU;YACR,GAAGH,WAAW;YACfI,SAAShB,MAAMc,UAAU;YACzB,8DAA8D;YAC9DL,QAAQ;;IAGd;IAEA,qBAAqB;IACrB,IAAI,CAACR,cAAcgB,MAAM,EAAE;QACzB,4EAA4E;QAC5E,uCAAuC;QACvC,qBAAO,qBAACC,eAAQ;IAClB;IAEA,IAAI,CAACV,SAAS;QACZ,4EAA4E;QAC5E,uCAAuC;QACvC,qBAAO,qBAACU,eAAQ;IAClB;IAEA,qBACE,qBAACC,cAAM;QACJ,GAAGP,WAAW;QACfQ,WAAWpB,MAAMoB,SAAS;QAC1BnB,eAAeA;QACfoB,SAAS;YACPlB,sBAAsB;QACxB;;AAGN"}