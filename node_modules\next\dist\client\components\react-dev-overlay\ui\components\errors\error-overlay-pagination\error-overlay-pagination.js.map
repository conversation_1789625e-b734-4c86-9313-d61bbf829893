{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/error-overlay-pagination/error-overlay-pagination.tsx"], "sourcesContent": ["import {\n  startTransition,\n  useCallback,\n  useEffect,\n  useRef,\n  useState,\n} from 'react'\nimport { LeftArrow } from '../../../icons/left-arrow'\nimport { RightArrow } from '../../../icons/right-arrow'\nimport type { ReadyRuntimeError } from '../../../../utils/get-error-by-type'\n\ntype ErrorPaginationProps = {\n  runtimeErrors: ReadyRuntimeError[]\n  activeIdx: number\n  onActiveIndexChange: (index: number) => void\n}\n\nexport function ErrorOverlayPagination({\n  runtimeErrors,\n  activeIdx,\n  onActiveIndexChange,\n}: ErrorPaginationProps) {\n  const handlePrevious = useCallback(\n    () =>\n      startTransition(() => {\n        if (activeIdx > 0) {\n          onActiveIndexChange(Math.max(0, activeIdx - 1))\n        }\n      }),\n    [activeIdx, onActiveIndexChange]\n  )\n\n  const handleNext = useCallback(\n    () =>\n      startTransition(() => {\n        if (activeIdx < runtimeErrors.length - 1) {\n          onActiveIndexChange(\n            Math.max(0, Math.min(runtimeErrors.length - 1, activeIdx + 1))\n          )\n        }\n      }),\n    [activeIdx, runtimeErrors.length, onActiveIndexChange]\n  )\n\n  const buttonLeft = useRef<HTMLButtonElement | null>(null)\n  const buttonRight = useRef<HTMLButtonElement | null>(null)\n\n  const [nav, setNav] = useState<HTMLElement | null>(null)\n  const onNav = useCallback((el: HTMLElement) => {\n    setNav(el)\n  }, [])\n\n  useEffect(() => {\n    if (nav == null) {\n      return\n    }\n\n    const root = nav.getRootNode()\n    const d = self.document\n\n    function handler(e: KeyboardEvent) {\n      if (e.key === 'ArrowLeft') {\n        e.preventDefault()\n        e.stopPropagation()\n        handlePrevious && handlePrevious()\n      } else if (e.key === 'ArrowRight') {\n        e.preventDefault()\n        e.stopPropagation()\n        handleNext && handleNext()\n      }\n    }\n\n    root.addEventListener('keydown', handler as EventListener)\n    if (root !== d) {\n      d.addEventListener('keydown', handler)\n    }\n    return function () {\n      root.removeEventListener('keydown', handler as EventListener)\n      if (root !== d) {\n        d.removeEventListener('keydown', handler)\n      }\n    }\n  }, [nav, handleNext, handlePrevious])\n\n  // Unlock focus for browsers like Firefox, that break all user focus if the\n  // currently focused item becomes disabled.\n  useEffect(() => {\n    if (nav == null) {\n      return\n    }\n\n    const root = nav.getRootNode()\n    // Always true, but we do this for TypeScript:\n    if (root instanceof ShadowRoot) {\n      const a = root.activeElement\n\n      if (activeIdx === 0) {\n        if (buttonLeft.current && a === buttonLeft.current) {\n          buttonLeft.current.blur()\n        }\n      } else if (activeIdx === runtimeErrors.length - 1) {\n        if (buttonRight.current && a === buttonRight.current) {\n          buttonRight.current.blur()\n        }\n      }\n    }\n  }, [nav, activeIdx, runtimeErrors.length])\n\n  return (\n    <nav\n      className=\"error-overlay-pagination dialog-exclude-closing-from-outside-click\"\n      ref={onNav}\n    >\n      <button\n        ref={buttonLeft}\n        type=\"button\"\n        disabled={activeIdx === 0}\n        aria-disabled={activeIdx === 0}\n        onClick={handlePrevious}\n        data-nextjs-dialog-error-previous\n        className=\"error-overlay-pagination-button\"\n      >\n        <LeftArrow\n          title=\"previous\"\n          className=\"error-overlay-pagination-button-icon\"\n        />\n      </button>\n      <div className=\"error-overlay-pagination-count\">\n        <span data-nextjs-dialog-error-index={activeIdx}>{activeIdx + 1}/</span>\n        <span data-nextjs-dialog-header-total-count>\n          {/* Display 1 out of 1 if there are no errors (e.g. for build errors). */}\n          {runtimeErrors.length || 1}\n        </span>\n      </div>\n      <button\n        ref={buttonRight}\n        type=\"button\"\n        // If no errors or the last error is active, disable the button.\n        disabled={activeIdx >= runtimeErrors.length - 1}\n        aria-disabled={activeIdx >= runtimeErrors.length - 1}\n        onClick={handleNext}\n        data-nextjs-dialog-error-next\n        className=\"error-overlay-pagination-button\"\n      >\n        <RightArrow\n          title=\"next\"\n          className=\"error-overlay-pagination-button-icon\"\n        />\n      </button>\n    </nav>\n  )\n}\n\nexport const styles = `\n  .error-overlay-pagination {\n    -webkit-font-smoothing: antialiased;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    gap: 8px;\n    width: fit-content;\n  }\n\n  .error-overlay-pagination-count {\n    color: var(--color-gray-900);\n    text-align: center;\n    font-size: var(--size-14);\n    font-weight: 500;\n    line-height: var(--size-16);\n    font-variant-numeric: tabular-nums;\n  }\n\n  .error-overlay-pagination-button {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n\n    width: var(--size-24);\n    height: var(--size-24);\n    background: var(--color-gray-300);\n    flex-shrink: 0;\n\n    border: none;\n    border-radius: var(--rounded-full);\n\n    svg {\n      width: var(--size-16);\n      height: var(--size-16);\n    }\n\n    &:focus-visible {\n      outline: var(--focus-ring);\n    }\n\n    &:not(:disabled):active {\n      background: var(--color-gray-500);\n    }\n\n    &:disabled {\n      opacity: 0.5;\n      cursor: not-allowed;\n    }\n  }\n\n  .error-overlay-pagination-button-icon {\n    color: var(--color-gray-1000);\n  }\n`\n"], "names": ["ErrorOverlayPagination", "styles", "runtimeErrors", "activeIdx", "onActiveIndexChange", "handlePrevious", "useCallback", "startTransition", "Math", "max", "handleNext", "length", "min", "buttonLeft", "useRef", "buttonRight", "nav", "set<PERSON><PERSON>", "useState", "onNav", "el", "useEffect", "root", "getRootNode", "d", "self", "document", "handler", "e", "key", "preventDefault", "stopPropagation", "addEventListener", "removeEventListener", "ShadowRoot", "a", "activeElement", "current", "blur", "className", "ref", "button", "type", "disabled", "aria-disabled", "onClick", "data-nextjs-dialog-error-previous", "LeftArrow", "title", "div", "span", "data-nextjs-dialog-error-index", "data-nextjs-dialog-header-total-count", "data-nextjs-dialog-error-next", "RightArrow"], "mappings": ";;;;;;;;;;;;;;;IAiBgBA,sBAAsB;eAAtBA;;IAwIHC,MAAM;eAANA;;;;uBAnJN;2BACmB;4BACC;AASpB,SAASD,uBAAuB,KAIhB;IAJgB,IAAA,EACrCE,aAAa,EACbC,SAAS,EACTC,mBAAmB,EACE,GAJgB;IAKrC,MAAMC,iBAAiBC,IAAAA,kBAAW,EAChC,IACEC,IAAAA,sBAAe,EAAC;YACd,IAAIJ,YAAY,GAAG;gBACjBC,oBAAoBI,KAAKC,GAAG,CAAC,GAAGN,YAAY;YAC9C;QACF,IACF;QAACA;QAAWC;KAAoB;IAGlC,MAAMM,aAAaJ,IAAAA,kBAAW,EAC5B,IACEC,IAAAA,sBAAe,EAAC;YACd,IAAIJ,YAAYD,cAAcS,MAAM,GAAG,GAAG;gBACxCP,oBACEI,KAAKC,GAAG,CAAC,GAAGD,KAAKI,GAAG,CAACV,cAAcS,MAAM,GAAG,GAAGR,YAAY;YAE/D;QACF,IACF;QAACA;QAAWD,cAAcS,MAAM;QAAEP;KAAoB;IAGxD,MAAMS,aAAaC,IAAAA,aAAM,EAA2B;IACpD,MAAMC,cAAcD,IAAAA,aAAM,EAA2B;IAErD,MAAM,CAACE,KAAKC,OAAO,GAAGC,IAAAA,eAAQ,EAAqB;IACnD,MAAMC,QAAQb,IAAAA,kBAAW,EAAC,CAACc;QACzBH,OAAOG;IACT,GAAG,EAAE;IAELC,IAAAA,gBAAS,EAAC;QACR,IAAIL,OAAO,MAAM;YACf;QACF;QAEA,MAAMM,OAAON,IAAIO,WAAW;QAC5B,MAAMC,IAAIC,KAAKC,QAAQ;QAEvB,SAASC,QAAQC,CAAgB;YAC/B,IAAIA,EAAEC,GAAG,KAAK,aAAa;gBACzBD,EAAEE,cAAc;gBAChBF,EAAEG,eAAe;gBACjB1B,kBAAkBA;YACpB,OAAO,IAAIuB,EAAEC,GAAG,KAAK,cAAc;gBACjCD,EAAEE,cAAc;gBAChBF,EAAEG,eAAe;gBACjBrB,cAAcA;YAChB;QACF;QAEAY,KAAKU,gBAAgB,CAAC,WAAWL;QACjC,IAAIL,SAASE,GAAG;YACdA,EAAEQ,gBAAgB,CAAC,WAAWL;QAChC;QACA,OAAO;YACLL,KAAKW,mBAAmB,CAAC,WAAWN;YACpC,IAAIL,SAASE,GAAG;gBACdA,EAAES,mBAAmB,CAAC,WAAWN;YACnC;QACF;IACF,GAAG;QAACX;QAAKN;QAAYL;KAAe;IAEpC,2EAA2E;IAC3E,2CAA2C;IAC3CgB,IAAAA,gBAAS,EAAC;QACR,IAAIL,OAAO,MAAM;YACf;QACF;QAEA,MAAMM,OAAON,IAAIO,WAAW;QAC5B,8CAA8C;QAC9C,IAAID,gBAAgBY,YAAY;YAC9B,MAAMC,IAAIb,KAAKc,aAAa;YAE5B,IAAIjC,cAAc,GAAG;gBACnB,IAAIU,WAAWwB,OAAO,IAAIF,MAAMtB,WAAWwB,OAAO,EAAE;oBAClDxB,WAAWwB,OAAO,CAACC,IAAI;gBACzB;YACF,OAAO,IAAInC,cAAcD,cAAcS,MAAM,GAAG,GAAG;gBACjD,IAAII,YAAYsB,OAAO,IAAIF,MAAMpB,YAAYsB,OAAO,EAAE;oBACpDtB,YAAYsB,OAAO,CAACC,IAAI;gBAC1B;YACF;QACF;IACF,GAAG;QAACtB;QAAKb;QAAWD,cAAcS,MAAM;KAAC;IAEzC,qBACE,sBAACK;QACCuB,WAAU;QACVC,KAAKrB;;0BAEL,qBAACsB;gBACCD,KAAK3B;gBACL6B,MAAK;gBACLC,UAAUxC,cAAc;gBACxByC,iBAAezC,cAAc;gBAC7B0C,SAASxC;gBACTyC,mCAAiC;gBACjCP,WAAU;0BAEV,cAAA,qBAACQ,oBAAS;oBACRC,OAAM;oBACNT,WAAU;;;0BAGd,sBAACU;gBAAIV,WAAU;;kCACb,sBAACW;wBAAKC,kCAAgChD;;4BAAYA,YAAY;4BAAE;;;kCAChE,qBAAC+C;wBAAKE,uCAAqC;kCAExClD,cAAcS,MAAM,IAAI;;;;0BAG7B,qBAAC8B;gBACCD,KAAKzB;gBACL2B,MAAK;gBACL,gEAAgE;gBAChEC,UAAUxC,aAAaD,cAAcS,MAAM,GAAG;gBAC9CiC,iBAAezC,aAAaD,cAAcS,MAAM,GAAG;gBACnDkC,SAASnC;gBACT2C,+BAA6B;gBAC7Bd,WAAU;0BAEV,cAAA,qBAACe,sBAAU;oBACTN,OAAM;oBACNT,WAAU;;;;;AAKpB;AAEO,MAAMtC,SAAU"}