{"version": 3, "sources": ["../../../src/build/templates/middleware.ts"], "sourcesContent": ["import type { AdapterOptions } from '../../server/web/adapter'\n\nimport '../../server/web/globals'\n\nimport { adapter } from '../../server/web/adapter'\n\n// Import the userland code.\nimport * as _mod from 'VAR_USERLAND'\nimport { edgeInstrumentationOnRequestError } from '../../server/web/globals'\nimport { isNextRouterError } from '../../client/components/is-next-router-error'\n\nconst mod = { ..._mod }\nconst handler = mod.middleware || mod.default\n\nconst page = 'VAR_DEFINITION_PAGE'\n\nif (typeof handler !== 'function') {\n  throw new Error(\n    `The Middleware \"${page}\" must export a \\`middleware\\` or a \\`default\\` function`\n  )\n}\n\n// Middleware will only sent out the FetchEvent to next server,\n// so load instrumentation module here and track the error inside middleware module.\nfunction errorHandledHandler(fn: AdapterOptions['handler']) {\n  return async (...args: Parameters<AdapterOptions['handler']>) => {\n    try {\n      return await fn(...args)\n    } catch (err) {\n      // In development, error the navigation API usage in runtime,\n      // since it's not allowed to be used in middleware as it's outside of react component tree.\n      if (process.env.NODE_ENV !== 'production') {\n        if (isNextRouterError(err)) {\n          err.message = `Next.js navigation API is not allowed to be used in Middleware.`\n          throw err\n        }\n      }\n      const req = args[0]\n      const url = new URL(req.url)\n      const resource = url.pathname + url.search\n      await edgeInstrumentationOnRequestError(\n        err,\n        {\n          path: resource,\n          method: req.method,\n          headers: Object.fromEntries(req.headers.entries()),\n        },\n        {\n          routerKind: 'Pages Router',\n          routePath: '/middleware',\n          routeType: 'middleware',\n          revalidateReason: undefined,\n        }\n      )\n\n      throw err\n    }\n  }\n}\n\nexport default function nHandler(\n  opts: Omit<AdapterOptions, 'IncrementalCache' | 'page' | 'handler'>\n) {\n  return adapter({\n    ...opts,\n    page,\n    handler: errorHandledHandler(handler),\n  })\n}\n"], "names": ["nH<PERSON><PERSON>", "mod", "_mod", "handler", "middleware", "default", "page", "Error", "error<PERSON>and<PERSON><PERSON>andler", "fn", "args", "err", "process", "env", "NODE_ENV", "isNextRouterError", "message", "req", "url", "URL", "resource", "pathname", "search", "edgeInstrumentationOnRequestError", "path", "method", "headers", "Object", "fromEntries", "entries", "routerKind", "routePath", "routeType", "revalidateReason", "undefined", "opts", "adapter"], "mappings": ";;;;+BA4DA;;;eAAwBA;;;yBA1DjB;yBAEiB;sEAGF;mCAEY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElC,MAAMC,MAAM;IAAE,GAAGC,aAAI;AAAC;AACtB,MAAMC,UAAUF,IAAIG,UAAU,IAAIH,IAAII,OAAO;AAE7C,MAAMC,OAAO;AAEb,IAAI,OAAOH,YAAY,YAAY;IACjC,MAAM,qBAEL,CAFK,IAAII,MACR,CAAC,gBAAgB,EAAED,KAAK,wDAAwD,CAAC,GAD7E,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,+DAA+D;AAC/D,oFAAoF;AACpF,SAASE,oBAAoBC,EAA6B;IACxD,OAAO,OAAO,GAAGC;QACf,IAAI;YACF,OAAO,MAAMD,MAAMC;QACrB,EAAE,OAAOC,KAAK;YACZ,6DAA6D;YAC7D,2FAA2F;YAC3F,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAIC,IAAAA,oCAAiB,EAACJ,MAAM;oBAC1BA,IAAIK,OAAO,GAAG,CAAC,+DAA+D,CAAC;oBAC/E,MAAML;gBACR;YACF;YACA,MAAMM,MAAMP,IAAI,CAAC,EAAE;YACnB,MAAMQ,MAAM,IAAIC,IAAIF,IAAIC,GAAG;YAC3B,MAAME,WAAWF,IAAIG,QAAQ,GAAGH,IAAII,MAAM;YAC1C,MAAMC,IAAAA,0CAAiC,EACrCZ,KACA;gBACEa,MAAMJ;gBACNK,QAAQR,IAAIQ,MAAM;gBAClBC,SAASC,OAAOC,WAAW,CAACX,IAAIS,OAAO,CAACG,OAAO;YACjD,GACA;gBACEC,YAAY;gBACZC,WAAW;gBACXC,WAAW;gBACXC,kBAAkBC;YACpB;YAGF,MAAMvB;QACR;IACF;AACF;AAEe,SAASX,SACtBmC,IAAmE;IAEnE,OAAOC,IAAAA,gBAAO,EAAC;QACb,GAAGD,IAAI;QACP7B;QACAH,SAASK,oBAAoBL;IAC/B;AACF"}