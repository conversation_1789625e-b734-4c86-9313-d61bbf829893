{"version": 3, "sources": ["../../../../src/build/webpack/plugins/jsconfig-paths-plugin.ts"], "sourcesContent": ["/**\n * This webpack resolver is largely based on TypeScript's \"paths\" handling\n * The TypeScript license can be found here:\n * https://github.com/microsoft/TypeScript/blob/214df64e287804577afa1fea0184c18c40f7d1ca/LICENSE.txt\n */\nimport path from 'path'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { debug } from 'next/dist/compiled/debug'\nimport type { ResolvedBaseUrl } from '../../load-jsconfig'\n\nconst log = debug('next:jsconfig-paths-plugin')\n\nexport interface Pattern {\n  prefix: string\n  suffix: string\n}\n\nconst asterisk = 0x2a\n\nexport function hasZeroOrOneAsteriskCharacter(str: string): boolean {\n  let seenAsterisk = false\n  for (let i = 0; i < str.length; i++) {\n    if (str.charCodeAt(i) === asterisk) {\n      if (!seenAsterisk) {\n        seenAsterisk = true\n      } else {\n        // have already seen asterisk\n        return false\n      }\n    }\n  }\n  return true\n}\n\n/**\n * Determines whether a path starts with a relative path component (i.e. `.` or `..`).\n */\nexport function pathIsRelative(testPath: string): boolean {\n  return /^\\.\\.?($|[\\\\/])/.test(testPath)\n}\n\nexport function tryParsePattern(pattern: string): Pattern | undefined {\n  // This should be verified outside of here and a proper error thrown.\n  const indexOfStar = pattern.indexOf('*')\n  return indexOfStar === -1\n    ? undefined\n    : {\n        prefix: pattern.slice(0, indexOfStar),\n        suffix: pattern.slice(indexOfStar + 1),\n      }\n}\n\nfunction isPatternMatch({ prefix, suffix }: Pattern, candidate: string) {\n  return (\n    candidate.length >= prefix.length + suffix.length &&\n    candidate.startsWith(prefix) &&\n    candidate.endsWith(suffix)\n  )\n}\n\n/** Return the object corresponding to the best pattern to match `candidate`. */\nexport function findBestPatternMatch<T>(\n  values: readonly T[],\n  getPattern: (value: T) => Pattern,\n  candidate: string\n): T | undefined {\n  let matchedValue: T | undefined\n  // use length of prefix as betterness criteria\n  let longestMatchPrefixLength = -1\n\n  for (const v of values) {\n    const pattern = getPattern(v)\n    if (\n      isPatternMatch(pattern, candidate) &&\n      pattern.prefix.length > longestMatchPrefixLength\n    ) {\n      longestMatchPrefixLength = pattern.prefix.length\n      matchedValue = v\n    }\n  }\n\n  return matchedValue\n}\n\n/**\n * patternStrings contains both pattern strings (containing \"*\") and regular strings.\n * Return an exact match if possible, or a pattern match, or undefined.\n * (These are verified by verifyCompilerOptions to have 0 or 1 \"*\" characters.)\n */\nexport function matchPatternOrExact(\n  patternStrings: readonly string[],\n  candidate: string\n): string | Pattern | undefined {\n  const patterns: Pattern[] = []\n  for (const patternString of patternStrings) {\n    if (!hasZeroOrOneAsteriskCharacter(patternString)) continue\n    const pattern = tryParsePattern(patternString)\n    if (pattern) {\n      patterns.push(pattern)\n    } else if (patternString === candidate) {\n      // pattern was matched as is - no need to search further\n      return patternString\n    }\n  }\n\n  return findBestPatternMatch(patterns, (_) => _, candidate)\n}\n\n/**\n * Tests whether a value is string\n */\nexport function isString(text: unknown): text is string {\n  return typeof text === 'string'\n}\n\n/**\n * Given that candidate matches pattern, returns the text matching the '*'.\n * E.g.: matchedText(tryParsePattern(\"foo*baz\"), \"foobarbaz\") === \"bar\"\n */\nexport function matchedText(pattern: Pattern, candidate: string): string {\n  return candidate.substring(\n    pattern.prefix.length,\n    candidate.length - pattern.suffix.length\n  )\n}\n\nexport function patternText({ prefix, suffix }: Pattern): string {\n  return `${prefix}*${suffix}`\n}\n\n/**\n * Calls the iterator function for each entry of the array\n * until the first result or error is reached\n */\nfunction forEachBail<TEntry>(\n  array: TEntry[],\n  iterator: (\n    entry: TEntry,\n    entryCallback: (err?: any, result?: any) => void\n  ) => void,\n  callback: (err?: any, result?: any) => void\n): void {\n  if (array.length === 0) return callback()\n\n  let i = 0\n  const next = () => {\n    let loop: boolean | undefined = undefined\n    iterator(array[i++], (err, result) => {\n      if (err || result !== undefined || i >= array.length) {\n        return callback(err, result)\n      }\n      if (loop === false) while (next());\n      loop = true\n    })\n    if (!loop) loop = false\n    return loop\n  }\n  while (next());\n}\n\nconst NODE_MODULES_REGEX = /node_modules/\n\ntype Paths = { [match: string]: string[] }\n\n/**\n * Handles tsconfig.json or jsconfig.js \"paths\" option for webpack\n * Largely based on how the TypeScript compiler handles it:\n * https://github.com/microsoft/TypeScript/blob/1a9c8197fffe3dace5f8dca6633d450a88cba66d/src/compiler/moduleNameResolver.ts#L1362\n */\n\ntype NonFunction<T> = T extends Function ? never : T\n\n// Pick the object type of ResolvePluginInstance\ntype ResolvePluginPlugin = NonFunction<webpack.ResolvePluginInstance>\nexport class JsConfigPathsPlugin implements ResolvePluginPlugin {\n  paths: Paths\n  resolvedBaseUrl: ResolvedBaseUrl\n  jsConfigPlugin: true\n\n  constructor(paths: Paths, resolvedBaseUrl: ResolvedBaseUrl) {\n    this.paths = paths\n    this.resolvedBaseUrl = resolvedBaseUrl\n    this.jsConfigPlugin = true\n    log('tsconfig.json or jsconfig.json paths: %O', paths)\n    log('resolved baseUrl: %s', resolvedBaseUrl)\n  }\n  apply(resolver: webpack.Resolver) {\n    const target = resolver.ensureHook('resolve')\n    resolver\n      .getHook('described-resolve')\n      .tapAsync(\n        'JsConfigPathsPlugin',\n        (\n          request: any,\n          resolveContext: any,\n          callback: (err?: any, result?: any) => void\n        ) => {\n          const resolvedBaseUrl = this.resolvedBaseUrl\n          if (resolvedBaseUrl === undefined) {\n            return callback()\n          }\n          const paths = this.paths\n          const pathsKeys = Object.keys(paths)\n\n          // If no aliases are added bail out\n          if (pathsKeys.length === 0) {\n            log('paths are empty, bailing out')\n            return callback()\n          }\n\n          const moduleName = request.request\n\n          // Exclude node_modules from paths support (speeds up resolving)\n          if (request.path.match(NODE_MODULES_REGEX)) {\n            log('skipping request as it is inside node_modules %s', moduleName)\n            return callback()\n          }\n\n          if (\n            path.posix.isAbsolute(moduleName) ||\n            (process.platform === 'win32' && path.win32.isAbsolute(moduleName))\n          ) {\n            log('skipping request as it is an absolute path %s', moduleName)\n            return callback()\n          }\n\n          if (pathIsRelative(moduleName)) {\n            log('skipping request as it is a relative path %s', moduleName)\n            return callback()\n          }\n\n          // log('starting to resolve request %s', moduleName)\n\n          // If the module name does not match any of the patterns in `paths` we hand off resolving to webpack\n          const matchedPattern = matchPatternOrExact(pathsKeys, moduleName)\n          if (!matchedPattern) {\n            log('moduleName did not match any paths pattern %s', moduleName)\n            return callback()\n          }\n\n          const matchedStar = isString(matchedPattern)\n            ? undefined\n            : matchedText(matchedPattern, moduleName)\n          const matchedPatternText = isString(matchedPattern)\n            ? matchedPattern\n            : patternText(matchedPattern)\n\n          let triedPaths = []\n\n          forEachBail(\n            paths[matchedPatternText],\n            (subst, pathCallback) => {\n              const curPath = matchedStar\n                ? subst.replace('*', matchedStar)\n                : subst\n              // Ensure .d.ts is not matched\n              if (curPath.endsWith('.d.ts')) {\n                // try next path candidate\n                return pathCallback()\n              }\n              const candidate = path.join(resolvedBaseUrl.baseUrl, curPath)\n              const obj = Object.assign({}, request, {\n                request: candidate,\n              })\n              resolver.doResolve(\n                target,\n                obj,\n                `Aliased with tsconfig.json or jsconfig.json ${matchedPatternText} to ${candidate}`,\n                resolveContext,\n                (resolverErr: any, resolverResult: any) => {\n                  if (resolverErr || resolverResult === undefined) {\n                    triedPaths.push(candidate)\n                    // try next path candidate\n                    return pathCallback()\n                  }\n                  return pathCallback(resolverErr, resolverResult)\n                }\n              )\n            },\n            callback\n          )\n        }\n      )\n  }\n}\n"], "names": ["JsConfigPathsPlugin", "findBestPatternMatch", "hasZeroOrOneAsteriskCharacter", "isString", "matchPatternOrExact", "matchedText", "pathIsRelative", "patternText", "tryParsePattern", "log", "debug", "asterisk", "str", "seenAsterisk", "i", "length", "charCodeAt", "testPath", "test", "pattern", "indexOfStar", "indexOf", "undefined", "prefix", "slice", "suffix", "isPatternMatch", "candidate", "startsWith", "endsWith", "values", "getPattern", "matchedValue", "longestMatchPrefixLength", "v", "patternStrings", "patterns", "patternString", "push", "_", "text", "substring", "forEachBail", "array", "iterator", "callback", "next", "loop", "err", "result", "NODE_MODULES_REGEX", "constructor", "paths", "resolvedBaseUrl", "jsConfigPlugin", "apply", "resolver", "target", "ensureH<PERSON>", "getHook", "tapAsync", "request", "resolveContext", "pathsKeys", "Object", "keys", "moduleName", "path", "match", "posix", "isAbsolute", "process", "platform", "win32", "matchedPattern", "matchedStar", "matchedPatternText", "triedPaths", "subst", "pathCallback", "curPath", "replace", "join", "baseUrl", "obj", "assign", "doResolve", "resolverErr", "resolverResult"], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;;;;;;;;;;;IA0KYA,mBAAmB;eAAnBA;;IAjHGC,oBAAoB;eAApBA;;IA1CAC,6BAA6B;eAA7BA;;IA4FAC,QAAQ;eAARA;;IAtBAC,mBAAmB;eAAnBA;;IA8BAC,WAAW;eAAXA;;IAlFAC,cAAc;eAAdA;;IAyFAC,WAAW;eAAXA;;IArFAC,eAAe;eAAfA;;;6DApCC;uBAEK;;;;;;AAGtB,MAAMC,MAAMC,IAAAA,YAAK,EAAC;AAOlB,MAAMC,WAAW;AAEV,SAAST,8BAA8BU,GAAW;IACvD,IAAIC,eAAe;IACnB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,IAAIG,MAAM,EAAED,IAAK;QACnC,IAAIF,IAAII,UAAU,CAACF,OAAOH,UAAU;YAClC,IAAI,CAACE,cAAc;gBACjBA,eAAe;YACjB,OAAO;gBACL,6BAA6B;gBAC7B,OAAO;YACT;QACF;IACF;IACA,OAAO;AACT;AAKO,SAASP,eAAeW,QAAgB;IAC7C,OAAO,kBAAkBC,IAAI,CAACD;AAChC;AAEO,SAAST,gBAAgBW,OAAe;IAC7C,qEAAqE;IACrE,MAAMC,cAAcD,QAAQE,OAAO,CAAC;IACpC,OAAOD,gBAAgB,CAAC,IACpBE,YACA;QACEC,QAAQJ,QAAQK,KAAK,CAAC,GAAGJ;QACzBK,QAAQN,QAAQK,KAAK,CAACJ,cAAc;IACtC;AACN;AAEA,SAASM,eAAe,EAAEH,MAAM,EAAEE,MAAM,EAAW,EAAEE,SAAiB;IACpE,OACEA,UAAUZ,MAAM,IAAIQ,OAAOR,MAAM,GAAGU,OAAOV,MAAM,IACjDY,UAAUC,UAAU,CAACL,WACrBI,UAAUE,QAAQ,CAACJ;AAEvB;AAGO,SAASxB,qBACd6B,MAAoB,EACpBC,UAAiC,EACjCJ,SAAiB;IAEjB,IAAIK;IACJ,8CAA8C;IAC9C,IAAIC,2BAA2B,CAAC;IAEhC,KAAK,MAAMC,KAAKJ,OAAQ;QACtB,MAAMX,UAAUY,WAAWG;QAC3B,IACER,eAAeP,SAASQ,cACxBR,QAAQI,MAAM,CAACR,MAAM,GAAGkB,0BACxB;YACAA,2BAA2Bd,QAAQI,MAAM,CAACR,MAAM;YAChDiB,eAAeE;QACjB;IACF;IAEA,OAAOF;AACT;AAOO,SAAS5B,oBACd+B,cAAiC,EACjCR,SAAiB;IAEjB,MAAMS,WAAsB,EAAE;IAC9B,KAAK,MAAMC,iBAAiBF,eAAgB;QAC1C,IAAI,CAACjC,8BAA8BmC,gBAAgB;QACnD,MAAMlB,UAAUX,gBAAgB6B;QAChC,IAAIlB,SAAS;YACXiB,SAASE,IAAI,CAACnB;QAChB,OAAO,IAAIkB,kBAAkBV,WAAW;YACtC,wDAAwD;YACxD,OAAOU;QACT;IACF;IAEA,OAAOpC,qBAAqBmC,UAAU,CAACG,IAAMA,GAAGZ;AAClD;AAKO,SAASxB,SAASqC,IAAa;IACpC,OAAO,OAAOA,SAAS;AACzB;AAMO,SAASnC,YAAYc,OAAgB,EAAEQ,SAAiB;IAC7D,OAAOA,UAAUc,SAAS,CACxBtB,QAAQI,MAAM,CAACR,MAAM,EACrBY,UAAUZ,MAAM,GAAGI,QAAQM,MAAM,CAACV,MAAM;AAE5C;AAEO,SAASR,YAAY,EAAEgB,MAAM,EAAEE,MAAM,EAAW;IACrD,OAAO,GAAGF,OAAO,CAAC,EAAEE,QAAQ;AAC9B;AAEA;;;CAGC,GACD,SAASiB,YACPC,KAAe,EACfC,QAGS,EACTC,QAA2C;IAE3C,IAAIF,MAAM5B,MAAM,KAAK,GAAG,OAAO8B;IAE/B,IAAI/B,IAAI;IACR,MAAMgC,OAAO;QACX,IAAIC,OAA4BzB;QAChCsB,SAASD,KAAK,CAAC7B,IAAI,EAAE,CAACkC,KAAKC;YACzB,IAAID,OAAOC,WAAW3B,aAAaR,KAAK6B,MAAM5B,MAAM,EAAE;gBACpD,OAAO8B,SAASG,KAAKC;YACvB;YACA,IAAIF,SAAS,OAAO,MAAOD;YAC3BC,OAAO;QACT;QACA,IAAI,CAACA,MAAMA,OAAO;QAClB,OAAOA;IACT;IACA,MAAOD;AACT;AAEA,MAAMI,qBAAqB;AAcpB,MAAMlD;IAKXmD,YAAYC,KAAY,EAAEC,eAAgC,CAAE;QAC1D,IAAI,CAACD,KAAK,GAAGA;QACb,IAAI,CAACC,eAAe,GAAGA;QACvB,IAAI,CAACC,cAAc,GAAG;QACtB7C,IAAI,4CAA4C2C;QAChD3C,IAAI,wBAAwB4C;IAC9B;IACAE,MAAMC,QAA0B,EAAE;QAChC,MAAMC,SAASD,SAASE,UAAU,CAAC;QACnCF,SACGG,OAAO,CAAC,qBACRC,QAAQ,CACP,uBACA,CACEC,SACAC,gBACAjB;YAEA,MAAMQ,kBAAkB,IAAI,CAACA,eAAe;YAC5C,IAAIA,oBAAoB/B,WAAW;gBACjC,OAAOuB;YACT;YACA,MAAMO,QAAQ,IAAI,CAACA,KAAK;YACxB,MAAMW,YAAYC,OAAOC,IAAI,CAACb;YAE9B,mCAAmC;YACnC,IAAIW,UAAUhD,MAAM,KAAK,GAAG;gBAC1BN,IAAI;gBACJ,OAAOoC;YACT;YAEA,MAAMqB,aAAaL,QAAQA,OAAO;YAElC,gEAAgE;YAChE,IAAIA,QAAQM,IAAI,CAACC,KAAK,CAAClB,qBAAqB;gBAC1CzC,IAAI,oDAAoDyD;gBACxD,OAAOrB;YACT;YAEA,IACEsB,aAAI,CAACE,KAAK,CAACC,UAAU,CAACJ,eACrBK,QAAQC,QAAQ,KAAK,WAAWL,aAAI,CAACM,KAAK,CAACH,UAAU,CAACJ,aACvD;gBACAzD,IAAI,iDAAiDyD;gBACrD,OAAOrB;YACT;YAEA,IAAIvC,eAAe4D,aAAa;gBAC9BzD,IAAI,gDAAgDyD;gBACpD,OAAOrB;YACT;YAEA,oDAAoD;YAEpD,oGAAoG;YACpG,MAAM6B,iBAAiBtE,oBAAoB2D,WAAWG;YACtD,IAAI,CAACQ,gBAAgB;gBACnBjE,IAAI,iDAAiDyD;gBACrD,OAAOrB;YACT;YAEA,MAAM8B,cAAcxE,SAASuE,kBACzBpD,YACAjB,YAAYqE,gBAAgBR;YAChC,MAAMU,qBAAqBzE,SAASuE,kBAChCA,iBACAnE,YAAYmE;YAEhB,IAAIG,aAAa,EAAE;YAEnBnC,YACEU,KAAK,CAACwB,mBAAmB,EACzB,CAACE,OAAOC;gBACN,MAAMC,UAAUL,cACZG,MAAMG,OAAO,CAAC,KAAKN,eACnBG;gBACJ,8BAA8B;gBAC9B,IAAIE,QAAQnD,QAAQ,CAAC,UAAU;oBAC7B,0BAA0B;oBAC1B,OAAOkD;gBACT;gBACA,MAAMpD,YAAYwC,aAAI,CAACe,IAAI,CAAC7B,gBAAgB8B,OAAO,EAAEH;gBACrD,MAAMI,MAAMpB,OAAOqB,MAAM,CAAC,CAAC,GAAGxB,SAAS;oBACrCA,SAASlC;gBACX;gBACA6B,SAAS8B,SAAS,CAChB7B,QACA2B,KACA,CAAC,4CAA4C,EAAER,mBAAmB,IAAI,EAAEjD,WAAW,EACnFmC,gBACA,CAACyB,aAAkBC;oBACjB,IAAID,eAAeC,mBAAmBlE,WAAW;wBAC/CuD,WAAWvC,IAAI,CAACX;wBAChB,0BAA0B;wBAC1B,OAAOoD;oBACT;oBACA,OAAOA,aAAaQ,aAAaC;gBACnC;YAEJ,GACA3C;QAEJ;IAEN;AACF"}