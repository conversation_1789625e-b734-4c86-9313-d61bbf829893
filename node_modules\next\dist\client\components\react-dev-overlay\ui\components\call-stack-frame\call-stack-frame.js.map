{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/call-stack-frame/call-stack-frame.tsx"], "sourcesContent": ["import type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport type { OriginalStackFrame } from '../../../utils/stack-frame'\n\nimport { HotlinkedText } from '../hot-linked-text'\nimport { ExternalIcon, SourceMappingErrorIcon } from '../../icons/external'\nimport { getFrameSource } from '../../../utils/stack-frame'\nimport { useOpenInEditor } from '../../utils/use-open-in-editor'\n\nexport const CallStackFrame: React.FC<{\n  frame: OriginalStackFrame\n}> = function CallStackFrame({ frame }) {\n  // TODO: ability to expand resolved frames\n\n  const f: StackFrame = frame.originalStackFrame ?? frame.sourceStackFrame\n  const hasSource = Boolean(frame.originalCodeFrame)\n  const open = useOpenInEditor(\n    hasSource\n      ? {\n          file: f.file,\n          lineNumber: f.lineNumber,\n          column: f.column,\n        }\n      : undefined\n  )\n\n  // Format method to strip out the webpack layer prefix.\n  // e.g. (app-pages-browser)/./app/page.tsx -> ./app/page.tsx\n  const formattedMethod = f.methodName.replace(/^\\([\\w-]+\\)\\//, '')\n\n  // Formatted file source could be empty. e.g. <anonymous> will be formatted to empty string,\n  // we'll skip rendering the frame in this case.\n  const fileSource = getFrameSource(f)\n\n  if (!fileSource) {\n    return null\n  }\n\n  return (\n    <div\n      data-nextjs-call-stack-frame\n      data-nextjs-call-stack-frame-no-source={!hasSource}\n      data-nextjs-call-stack-frame-ignored={frame.ignored}\n    >\n      <div className=\"call-stack-frame-method-name\">\n        <HotlinkedText text={formattedMethod} />\n        {hasSource && (\n          <button onClick={open} className=\"open-in-editor-button\">\n            <ExternalIcon width={16} height={16} />\n          </button>\n        )}\n        {frame.error ? (\n          <button\n            className=\"source-mapping-error-button\"\n            onClick={() => console.error(frame.reason)}\n            title=\"Sourcemapping failed. Click to log cause of error.\"\n          >\n            <SourceMappingErrorIcon width={16} height={16} />\n          </button>\n        ) : null}\n      </div>\n      <span\n        className=\"call-stack-frame-file-source\"\n        data-has-source={hasSource}\n      >\n        {fileSource}\n      </span>\n    </div>\n  )\n}\n\nexport const CALL_STACK_FRAME_STYLES = `\n  [data-nextjs-call-stack-frame-no-source] {\n    padding: 6px 8px;\n    margin-bottom: 4px;\n\n    border-radius: var(--rounded-lg);\n  }\n\n  [data-nextjs-call-stack-frame-no-source]:last-child {\n    margin-bottom: 0;\n  }\n\n  [data-nextjs-call-stack-frame-ignored=\"true\"] {\n    opacity: 0.6;\n  }\n\n  [data-nextjs-call-stack-frame] {\n    user-select: text;\n    display: block;\n    box-sizing: border-box;\n\n    user-select: text;\n    -webkit-user-select: text;\n    -moz-user-select: text;\n    -ms-user-select: text;\n\n    padding: 6px 8px;\n\n    border-radius: var(--rounded-lg);\n  }\n\n  .call-stack-frame-method-name {\n    display: flex;\n    align-items: center;\n    gap: 4px;\n\n    margin-bottom: 4px;\n    font-family: var(--font-stack-monospace);\n\n    color: var(--color-gray-1000);\n    font-size: var(--size-14);\n    font-weight: 500;\n    line-height: var(--size-20);\n\n    svg {\n      width: var(--size-16px);\n      height: var(--size-16px);\n    }\n  }\n\n  .open-in-editor-button, .source-mapping-error-button {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: var(--rounded-full);\n    padding: 4px;\n    color: var(--color-font);\n\n    svg {\n      width: var(--size-16);\n      height: var(--size-16);\n    }\n\n    &:focus-visible {\n      outline: var(--focus-ring);\n      outline-offset: -2px;\n    }\n\n    &:hover {\n      background: var(--color-gray-100);\n    }\n  }\n\n  .call-stack-frame-file-source {\n    color: var(--color-gray-900);\n    font-size: var(--size-14);\n    line-height: var(--size-20);\n  }\n`\n"], "names": ["CALL_STACK_FRAME_STYLES", "CallStackFrame", "frame", "f", "originalStackFrame", "sourceStackFrame", "hasSource", "Boolean", "originalCodeFrame", "open", "useOpenInEditor", "file", "lineNumber", "column", "undefined", "formattedMethod", "methodName", "replace", "fileSource", "getFrameSource", "div", "data-nextjs-call-stack-frame", "data-nextjs-call-stack-frame-no-source", "data-nextjs-call-stack-frame-ignored", "ignored", "className", "HotlinkedText", "text", "button", "onClick", "ExternalIcon", "width", "height", "error", "console", "reason", "title", "SourceMappingErrorIcon", "span", "data-has-source"], "mappings": ";;;;;;;;;;;;;;;IAsEaA,uBAAuB;eAAvBA;;IA9DAC,cAAc;eAAdA;;;;+BALiB;0BACuB;4BACtB;iCACC;AAEzB,MAAMA,iBAER,SAASA,eAAe,KAAS;IAAT,IAAA,EAAEC,KAAK,EAAE,GAAT;QAGLA;IAFtB,0CAA0C;IAE1C,MAAMC,IAAgBD,CAAAA,4BAAAA,MAAME,kBAAkB,YAAxBF,4BAA4BA,MAAMG,gBAAgB;IACxE,MAAMC,YAAYC,QAAQL,MAAMM,iBAAiB;IACjD,MAAMC,OAAOC,IAAAA,gCAAe,EAC1BJ,YACI;QACEK,MAAMR,EAAEQ,IAAI;QACZC,YAAYT,EAAES,UAAU;QACxBC,QAAQV,EAAEU,MAAM;IAClB,IACAC;IAGN,uDAAuD;IACvD,4DAA4D;IAC5D,MAAMC,kBAAkBZ,EAAEa,UAAU,CAACC,OAAO,CAAC,iBAAiB;IAE9D,4FAA4F;IAC5F,+CAA+C;IAC/C,MAAMC,aAAaC,IAAAA,0BAAc,EAAChB;IAElC,IAAI,CAACe,YAAY;QACf,OAAO;IACT;IAEA,qBACE,sBAACE;QACCC,8BAA4B;QAC5BC,0CAAwC,CAAChB;QACzCiB,wCAAsCrB,MAAMsB,OAAO;;0BAEnD,sBAACJ;gBAAIK,WAAU;;kCACb,qBAACC,4BAAa;wBAACC,MAAMZ;;oBACpBT,2BACC,qBAACsB;wBAAOC,SAASpB;wBAAMgB,WAAU;kCAC/B,cAAA,qBAACK,sBAAY;4BAACC,OAAO;4BAAIC,QAAQ;;;oBAGpC9B,MAAM+B,KAAK,iBACV,qBAACL;wBACCH,WAAU;wBACVI,SAAS,IAAMK,QAAQD,KAAK,CAAC/B,MAAMiC,MAAM;wBACzCC,OAAM;kCAEN,cAAA,qBAACC,gCAAsB;4BAACN,OAAO;4BAAIC,QAAQ;;yBAE3C;;;0BAEN,qBAACM;gBACCb,WAAU;gBACVc,mBAAiBjC;0BAEhBY;;;;AAIT;AAEO,MAAMlB,0BAA2B"}