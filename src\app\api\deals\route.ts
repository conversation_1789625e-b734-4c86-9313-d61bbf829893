import { NextResponse } from 'next/server';
import { getDealsFromEnv, Deal } from '@/lib/dealsParser';

// Cache deals for 5 minutes to avoid repeated thumbnail extraction
let cachedDeals: Deal[] | null = null;
let cacheTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export async function GET() {
  try {
    const now = Date.now();
    
    // Return cached deals if still valid
    if (cachedDeals && (now - cacheTime) < CACHE_DURATION) {
      return NextResponse.json({ deals: cachedDeals });
    }
    
    // Fetch fresh deals
    const deals = await getDealsFromEnv();
    
    // Update cache
    cachedDeals = deals;
    cacheTime = now;
    
    return NextResponse.json({ deals });
  } catch (error) {
    console.error('Error fetching deals:', error);
    return NextResponse.json({ error: 'Failed to fetch deals' }, { status: 500 });
  }
}

// Force refresh cache (useful for development)
export async function POST() {
  try {
    cachedDeals = null;
    cacheTime = 0;
    
    const deals = await getDealsFromEnv();
    cachedDeals = deals;
    cacheTime = Date.now();
    
    return NextResponse.json({ deals, message: 'Cache refreshed' });
  } catch (error) {
    console.error('Error refreshing deals cache:', error);
    return NextResponse.json({ error: 'Failed to refresh deals' }, { status: 500 });
  }
}
