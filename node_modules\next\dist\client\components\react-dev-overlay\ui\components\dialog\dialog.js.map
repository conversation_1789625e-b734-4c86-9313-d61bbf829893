{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/dialog/dialog.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { useOnClickOutside } from '../../hooks/use-on-click-outside'\nimport { useMeasureHeight } from '../../hooks/use-measure-height'\n\nexport type DialogProps = {\n  children?: React.ReactNode\n  type: 'error' | 'warning'\n  'aria-labelledby': string\n  'aria-describedby': string\n  className?: string\n  onClose?: () => void\n  dialogResizerRef?: React.RefObject<HTMLDivElement | null>\n} & React.HTMLAttributes<HTMLDivElement>\n\nconst CSS_SELECTORS_TO_EXCLUDE_ON_CLICK_OUTSIDE = [\n  '[data-next-mark]',\n  '[data-issues-open]',\n  '#nextjs-dev-tools-menu',\n  '[data-nextjs-error-overlay-nav]',\n  '[data-info-popover]',\n]\n\nconst Dialog: React.FC<DialogProps> = function Dialog({\n  children,\n  type,\n  className,\n  onClose,\n  'aria-labelledby': ariaLabelledBy,\n  'aria-describedby': ariaDescribedBy,\n  dialogResizerRef,\n  ...props\n}) {\n  const dialogRef = React.useRef<HTMLDivElement | null>(null)\n  const [role, setRole] = React.useState<string | undefined>(\n    typeof document !== 'undefined' && document.hasFocus()\n      ? 'dialog'\n      : undefined\n  )\n\n  const ref = React.useRef<HTMLDivElement | null>(null)\n  const [height, pristine] = useMeasureHeight(ref)\n\n  useOnClickOutside(\n    dialogRef.current,\n    CSS_SELECTORS_TO_EXCLUDE_ON_CLICK_OUTSIDE,\n    (e) => {\n      e.preventDefault()\n      return onClose?.()\n    }\n  )\n\n  React.useEffect(() => {\n    if (dialogRef.current == null) {\n      return\n    }\n\n    function handleFocus() {\n      // safari will force itself as the active application when a background page triggers any sort of autofocus\n      // this is a workaround to only set the dialog role if the document has focus\n      setRole(document.hasFocus() ? 'dialog' : undefined)\n    }\n\n    window.addEventListener('focus', handleFocus)\n    window.addEventListener('blur', handleFocus)\n    return () => {\n      window.removeEventListener('focus', handleFocus)\n      window.removeEventListener('blur', handleFocus)\n    }\n  }, [])\n\n  React.useEffect(() => {\n    const dialog = dialogRef.current\n    const root = dialog?.getRootNode()\n    const initialActiveElement =\n      root instanceof ShadowRoot ? (root?.activeElement as HTMLElement) : null\n\n    // Trap focus within the dialog\n    dialog?.focus()\n\n    return () => {\n      // Blur first to avoid getting stuck, in case `activeElement` is missing\n      dialog?.blur()\n      // Restore focus to the previously active element\n      initialActiveElement?.focus()\n    }\n  }, [])\n\n  return (\n    <div\n      ref={dialogRef}\n      tabIndex={-1}\n      data-nextjs-dialog\n      role={role}\n      aria-labelledby={ariaLabelledBy}\n      aria-describedby={ariaDescribedBy}\n      aria-modal=\"true\"\n      className={className}\n      onKeyDown={(e) => {\n        if (e.key === 'Escape') {\n          onClose?.()\n        }\n      }}\n      {...props}\n    >\n      <div\n        ref={dialogResizerRef}\n        data-nextjs-dialog-sizer\n        // [x] Don't animate on initial load\n        // [x] No duplicate elements\n        // [x] Responds to content growth\n        style={{\n          height,\n          transition: pristine ? undefined : 'height 250ms var(--timing-swift)',\n        }}\n      >\n        <div ref={ref}>{children}</div>\n      </div>\n    </div>\n  )\n}\n\nexport { Dialog }\n"], "names": ["Dialog", "CSS_SELECTORS_TO_EXCLUDE_ON_CLICK_OUTSIDE", "children", "type", "className", "onClose", "ariaLabelledBy", "ariaDescribedBy", "dialogResizerRef", "props", "dialogRef", "React", "useRef", "role", "setRole", "useState", "document", "hasFocus", "undefined", "ref", "height", "pristine", "useMeasureHeight", "useOnClickOutside", "current", "e", "preventDefault", "useEffect", "handleFocus", "window", "addEventListener", "removeEventListener", "dialog", "root", "getRootNode", "initialActiveElement", "ShadowRoot", "activeElement", "focus", "blur", "div", "tabIndex", "data-nextjs-dialog", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "aria-modal", "onKeyDown", "key", "data-nextjs-dialog-sizer", "style", "transition"], "mappings": ";;;;+BAyHSA;;;eAAAA;;;;;iEAzHc;mCACW;kCACD;AAYjC,MAAMC,4CAA4C;IAChD;IACA;IACA;IACA;IACA;CACD;AAED,MAAMD,SAAgC,SAASA,OAAO,KASrD;IATqD,IAAA,EACpDE,QAAQ,EACRC,IAAI,EACJC,SAAS,EACTC,OAAO,EACP,mBAAmBC,cAAc,EACjC,oBAAoBC,eAAe,EACnCC,gBAAgB,EAChB,GAAGC,OACJ,GATqD;IAUpD,MAAMC,YAAYC,OAAMC,MAAM,CAAwB;IACtD,MAAM,CAACC,MAAMC,QAAQ,GAAGH,OAAMI,QAAQ,CACpC,OAAOC,aAAa,eAAeA,SAASC,QAAQ,KAChD,WACAC;IAGN,MAAMC,MAAMR,OAAMC,MAAM,CAAwB;IAChD,MAAM,CAACQ,QAAQC,SAAS,GAAGC,IAAAA,kCAAgB,EAACH;IAE5CI,IAAAA,oCAAiB,EACfb,UAAUc,OAAO,EACjBvB,2CACA,CAACwB;QACCA,EAAEC,cAAc;QAChB,OAAOrB,2BAAAA;IACT;IAGFM,OAAMgB,SAAS,CAAC;QACd,IAAIjB,UAAUc,OAAO,IAAI,MAAM;YAC7B;QACF;QAEA,SAASI;YACP,2GAA2G;YAC3G,6EAA6E;YAC7Ed,QAAQE,SAASC,QAAQ,KAAK,WAAWC;QAC3C;QAEAW,OAAOC,gBAAgB,CAAC,SAASF;QACjCC,OAAOC,gBAAgB,CAAC,QAAQF;QAChC,OAAO;YACLC,OAAOE,mBAAmB,CAAC,SAASH;YACpCC,OAAOE,mBAAmB,CAAC,QAAQH;QACrC;IACF,GAAG,EAAE;IAELjB,OAAMgB,SAAS,CAAC;QACd,MAAMK,SAAStB,UAAUc,OAAO;QAChC,MAAMS,OAAOD,0BAAAA,OAAQE,WAAW;QAChC,MAAMC,uBACJF,gBAAgBG,aAAcH,wBAAAA,KAAMI,aAAa,GAAmB;QAEtE,+BAA+B;QAC/BL,0BAAAA,OAAQM,KAAK;QAEb,OAAO;YACL,wEAAwE;YACxEN,0BAAAA,OAAQO,IAAI;YACZ,iDAAiD;YACjDJ,wCAAAA,qBAAsBG,KAAK;QAC7B;IACF,GAAG,EAAE;IAEL,qBACE,qBAACE;QACCrB,KAAKT;QACL+B,UAAU,CAAC;QACXC,oBAAkB;QAClB7B,MAAMA;QACN8B,mBAAiBrC;QACjBsC,oBAAkBrC;QAClBsC,cAAW;QACXzC,WAAWA;QACX0C,WAAW,CAACrB;YACV,IAAIA,EAAEsB,GAAG,KAAK,UAAU;gBACtB1C,2BAAAA;YACF;QACF;QACC,GAAGI,KAAK;kBAET,cAAA,qBAAC+B;YACCrB,KAAKX;YACLwC,0BAAwB;YACxB,oCAAoC;YACpC,4BAA4B;YAC5B,iCAAiC;YACjCC,OAAO;gBACL7B;gBACA8B,YAAY7B,WAAWH,YAAY;YACrC;sBAEA,cAAA,qBAACsB;gBAAIrB,KAAKA;0BAAMjB;;;;AAIxB"}